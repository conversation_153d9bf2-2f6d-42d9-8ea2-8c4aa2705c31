#!/usr/bin/env python3
"""
test_search_product_from_es.py

针对 search_product_from_es 方法的测试文件
支持命令行参数指定搜索词，默认测试"椰子水,淡奶油"
"""

import sys
import os
import argparse
import json
from typing import List, Dict, Any

# 添加项目根目录到Python路径，确保能正确导入模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.services.agent.tools.product_search_tool import search_product_from_es
from src.utils.logger import logger

def print_product_results(product_groups: List[Dict[str, Any]], query: str) -> None:
    """
    格式化打印搜索结果（新的pd_id分组结构）
    
    Args:
        product_groups: 按pd_id分组的商品列表
        query: 搜索查询词
    """
    print(f"\n{'='*60}")
    print(f"搜索关键词: {query}")
    print(f"{'='*60}")
    
    if not product_groups:
        print("❌ 未找到匹配的商品")
        return
    
    total_skus = sum(len(group.get('sku_list', [])) for group in product_groups)
    print(f"✅ 找到 {len(product_groups)} 个商品组，共 {total_skus} 个SKU:")
    print("-" * 60)
    
    for i, group in enumerate(product_groups, 1):
        print(f"{i}. 【商品组 pd_id: {group.get('pd_id', 'N/A')}】")
        print(f"   商品名称: {group.get('title', 'N/A')}")
        print(f"   品牌: {group.get('brand_name', 'N/A') or '无品牌信息'}")
        print(f"   分类: {group.get('category_path', 'N/A')} ({group.get('category_type_description', 'N/A')})")
        
        sku_list = group.get('sku_list', [])
        print(f"   包含 {len(sku_list)} 个SKU:")
        
        for j, sku in enumerate(sku_list, 1):
            print(f"     {i}.{j} SKU: {sku.get('sku', 'N/A')}")
            print(f"         规格: {sku.get('specification', 'N/A')}")
            print(f"         价格: {sku.get('price', 0.0):.2f} 元")
            print(f"         库存: {sku.get('store_quantity', 0)} 件")
            print(f"         类型: {sku.get('sub_type', 'N/A')}")
            if j < len(sku_list):
                print()
        
        print("-" * 60)

def test_single_product(query: str, specification: str = None, is_shunluda: bool = False) -> List[Dict[str, Any]]:
    """
    测试单个商品搜索
    
    Args:
        query: 搜索关键词
        specification: 可选的规格信息
        is_shunluda: 是否只搜索顺鹿达商品
        
    Returns:
        搜索结果列表
    """
    try:
        logger.info(f"开始搜索商品: {query}")
        
        # 调用搜索方法
        products = search_product_from_es(
            product_name=query,
            specification=specification,
            is_shunluda=is_shunluda,
            size=20
        )
        
        # 打印结果
        print_product_results(products, query)
        
        return products
        
    except Exception as e:
        logger.error(f"搜索商品 '{query}' 时发生错误: {str(e)}")
        print(f"❌ 搜索失败: {str(e)}")
        return []

def test_batch_products(queries: List[str]) -> Dict[str, List[Dict[str, Any]]]:
    """
    批量测试多个商品搜索
    
    Args:
        queries: 搜索关键词列表
        
    Returns:
        所有搜索结果的字典
    """
    results = {}
    
    print(f"\n🔍 开始批量测试 {len(queries)} 个商品搜索...")
    
    for query in queries:
        query = query.strip()
        if query:
            results[query] = test_single_product(query)
    
    return results

def analyze_search_results(all_results: Dict[str, List[Dict[str, Any]]]) -> None:
    """
    分析搜索结果的统计信息（适配新的pd_id分组结构）
    
    Args:
        all_results: 所有搜索结果
    """
    print(f"\n📊 搜索结果统计分析")
    print("=" * 60)
    
    total_queries = len(all_results)
    successful_queries = sum(1 for results in all_results.values() if results)
    total_groups = sum(len(results) for results in all_results.values())
    total_skus = 0
    
    for query, groups in all_results.items():
        for group in groups:
            total_skus += len(group.get('sku_list', []))
    
    print(f"总搜索次数: {total_queries}")
    print(f"成功搜索次数: {successful_queries}")
    print(f"成功率: {(successful_queries/total_queries*100):.1f}%" if total_queries > 0 else "0%")
    print(f"找到商品组总数: {total_groups}")
    print(f"找到SKU总数: {total_skus}")
    print(f"平均每次搜索结果数: {(total_groups/successful_queries):.1f}" if successful_queries > 0 else "0")
    print(f"平均每组SKU数: {(total_skus/total_groups):.1f}" if total_groups > 0 else "0")
    
    # 分析各类商品分布
    category_stats = {}
    brand_stats = {}
    sub_type_stats = {}
    
    for query, groups in all_results.items():
        for group in groups:
            # 统计分类（按商品组统计）
            category = group.get('category_type_description', '其他')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            # 统计品牌（按商品组统计）
            brand = group.get('brand_name', '无品牌')
            if brand:
                brand_stats[brand] = brand_stats.get(brand, 0) + 1
            
            # 统计子类型（按SKU统计）
            for sku in group.get('sku_list', []):
                sub_type = sku.get('sub_type', '其他')
                sub_type_stats[sub_type] = sub_type_stats.get(sub_type, 0) + 1
    
    if category_stats:
        print("\n📋 商品分类分布:")
        for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {category}: {count} 个商品组")
    
    if brand_stats:
        print("\n🏷️  品牌分布 (Top 10):")
        for brand, count in sorted(brand_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {brand}: {count} 个商品组")
    
    if sub_type_stats:
        print("\n🔖 商品类型分布:")
        for sub_type, count in sorted(sub_type_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {sub_type}: {count} 个SKU")

def test_advanced_scenarios():
    """
    测试高级搜索场景
    """
    print(f"\n🧪 高级搜索场景测试")
    print("=" * 60)
    
    # 1. 测试顺鹿达商品搜索
    print("\n1. 测试顺鹿达商品搜索:")
    test_single_product("椰子水", is_shunluda=True)
    
    # 2. 测试带规格的搜索
    print("\n2. 测试带规格信息的搜索:")
    test_single_product("淡奶油", specification="1L")
    
    # 3. 测试空结果的情况
    print("\n3. 测试不存在的商品:")
    test_single_product("这是一个不存在的商品名称12345")

def export_results_to_json(results: Dict[str, List[Dict[str, Any]]], filename: str = "search_results.json") -> None:
    """
    导出搜索结果到JSON文件
    
    Args:
        results: 搜索结果
        filename: 输出文件名
    """
    try:
        output_path = os.path.join(os.path.dirname(__file__), filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 搜索结果已导出到: {output_path}")
    except Exception as e:
        logger.error(f"导出结果失败: {str(e)}")
        print(f"❌ 导出失败: {str(e)}")

def main():
    """
    主测试函数
    """
    parser = argparse.ArgumentParser(
        description="测试 search_product_from_es 方法",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python test_search_product_from_es.py                    # 使用默认搜索词
  python test_search_product_from_es.py --queries "牛奶,面包,苹果"  # 指定搜索词
  python test_search_product_from_es.py --advanced         # 运行高级测试
  python test_search_product_from_es.py --export results.json # 导出结果
        """
    )
    
    parser.add_argument(
        '--queries', 
        type=str,
        default="椰子水,淡奶油",
        help='搜索关键词，多个词用逗号分隔 (默认: "椰子水,淡奶油")'
    )
    
    parser.add_argument(
        '--advanced',
        action='store_true',
        help='运行高级搜索场景测试'
    )
    
    parser.add_argument(
        '--export',
        type=str,
        metavar='FILENAME',
        help='导出搜索结果到JSON文件'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='显示详细日志信息'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.INFO)
    
    print("🚀 开始测试 search_product_from_es 方法")
    print("=" * 60)
    
    # 解析搜索关键词
    queries = [q.strip() for q in args.queries.split(',') if q.strip()]
    
    if not queries:
        print("❌ 错误: 未提供有效的搜索关键词")
        return
    
    print(f"📝 测试搜索词: {', '.join(queries)}")
    
    # 执行批量搜索测试
    all_results = test_batch_products(queries)
    
    # 分析搜索结果
    analyze_search_results(all_results)
    
    # 运行高级测试场景
    if args.advanced:
        test_advanced_scenarios()
    
    # 导出结果
    if args.export:
        export_results_to_json(all_results, args.export)
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    main()
