# MySQL 配置项
DB_HOST=rr-bp19zkvz53m17h8qsmo.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_USER=xianmu_ai
DB_PASSWORD=Xianmu_ai
DB_NAME=xianmudb
# 超时时间：秒数
DB_CONNECT_TIMEOUT=2

CHATBI_MYSQL_DATABASE=chatbi

# --- Model Provider Configuration ---
# 鲜沐内部API配置
PROVIDER_XM_API_KEY=sk-Is03LB8g58Qz41bdQKip5g
PROVIDER_XM_API_BASE=https://litellm-test.summerfarm.net/v1
PROVIDER_XM_DEFAULT_MODEL=deepseek-v3-250324
PROVIDER_XM_FAST_MODEL=deepseek-v3-250324

# OpenRouter API配置
PROVIDER_OPENROUTER_API_KEY=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
PROVIDER_OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PROVIDER_OPENROUTER_DEFAULT_MODEL=google/gemini-2.5-pro
PROVIDER_OPENROUTER_FAST_MODEL=openai/gpt-oss-120b
PROVIDER_OPENROUTER_CLAUDE_MODEL=anthropic/claude-sonnet-4

# 默认提供者配置
DEFAULT_MODEL_PROVIDER=openrouter

# --- RAG Configuration ---
TOP_TABLE_CNT=20

# --- ChatBI Configuration ---
# 当使用本地localhost时，请使用这个：
# CHAT_BI_HOST_NAME=http://127.0.0.1:5700

# 默认使用这个：
CHAT_BI_HOST_NAME=https://chat-bi.summerfarm.net

# --- Flask Configuration (Optional) ---
FLASK_ENV=production # Set to 'production' for deployment
FLASK_DEBUG=0 # Enable debug mode (1) or disable (0)

# SQL返回的行数大于这个值时，默认上传到飞书
MIN_ROWS_TO_IMPORT=20

# 强制刷新token的时间间隔（分钟）
MINUTES_TO_FORCE_REFRESH_TOKEN=10

# 飞书应用的 App ID 和 App Secret
FEISHU_APP_ID=cli_a450bff26fbbd00d
FEISHU_APP_SECRET=uQolgem8B8fwuTlsER0bZdUe7xZjueHU

# 是否启用飞书消息处理
ENABLE_BOT_MESSAGE_PROCESSING=true

# 应用根目录(解决nginx部署问题)
APPLICATION_ROOT=

# 如果超过400行，则对喂给AI的数据进行截断
MIN_ROWS_TO_CUTOFF=400

# 如果超过20行，则自动上传到飞书多维表格
MIN_ROWS_TO_IMPORT=20

# Dashboard的管理员列表
DASHBOARD_ADMINS=唐鹏,康凯,王剑锋,施正豪,江涛,李茂源,李钦,胡译丰,张志强

# 飞书卡片超时时间，超过这个时间后就不可更新了（可用于调整测试）
STREAM_TIMEOUT_MINUTES=10

# Bad Case通知群聊ID（可选）
# 当用户标记对话为Bad Case时，会发送通知到此群聊
# 如果不配置此项，则不会发送通知
BAD_CASE_NOTIFICATION_CHAT_ID=oc_61c2a621535eacb60c33711c09231781

# 群聊时，@机器人的名称
FEISHU_BOT_NAME=鲜沐ChatBI

# 当用户进入和机器人进行P2P聊天时，如果距离上次发送消息的时间小于这个值（单位：小时），则不发送欢迎消息
USER_ENTERED_THRESHOLD=24

# 是否开启model跟踪，本地环境可以开启，线上环境尽量不要开启
OUTPUT_MODEL_PROVIDER=false

ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tQzmpz2nQEWdiqvQGsc
ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************