CREATE TABLE `xianmudb`.`after_sale_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `after_sale_order_no` varchar(36) NOT NULL COMMENT '售后订单编号，唯一标识售后申请',
  `m_id` bigint(30) DEFAULT NULL COMMENT '售后门店ID，关联merchant.m_id',
  `order_no` varchar(36) DEFAULT NULL COMMENT '原订单编号，关联orders.order_no',
  `deliveryed` int(11) DEFAULT NULL COMMENT '0:未到货售后, 1:已到货售后',
  `is_full` tinyint(2) DEFAULT '0' COMMENT '是否全额退款,1:是,0:否',
  `after_sale_unit` varchar(5) DEFAULT NULL COMMENT '售后单位，如个、件、箱、盒、g、kg、ml、L等, 通常和after_sale_proof.quantity一起使用',
  `sku` varchar(30) DEFAULT NULL COMMENT '售后商品SKU，为空表示整单售后，不为空表示部分售后。关联order_item.sku, 也可关联inventory.sku',
  `status` int(2) DEFAULT NULL COMMENT '售后状态售后状态：0:审核中, 指待售后人员审核; 1:审批中, 指售后人员已审核通过、待售后主管进一步审批中; 2:成功; 3:失败, 指审核人员拒绝了本次售后; 4:补充凭证，指审核人员要求客户提供额外信息; 11:取消, 指客户自己取消了本次售后请求。',
  `after_sale_order_status` tinyint(4) DEFAULT '0' COMMENT '售后单类型：0:普通售后单，1:拦截售后单',
  `add_time` datetime DEFAULT NULL COMMENT '申请时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `in_after_no` (`after_sale_order_no`),
  KEY `in_sku` (`sku`),
  KEY `after_sale_order_index` (`m_id`,`type`,`status`,`add_time`),
  KEY `idx_addtime` (`add_time`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='售后单明细，记录订单的售后申请的基本信息.';