
CREATE TABLE `xianmudb`.`stock_allocation_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `list_no` varchar(45) DEFAULT NULL COMMENT '调拨单号',
  `create_admin` int(11) DEFAULT NULL COMMENT '单据创建人id',
  `out_store` int(11) DEFAULT NULL COMMENT '调出仓编号，warehouse_storage_center表中的warehouse_no字段',
  `in_store` int(11) DEFAULT NULL COMMENT '调入仓编号，warehouse_storage_center表中的warehouse_no字段',
  `out_time` datetime DEFAULT NULL COMMENT '订单确认时间，(不可靠，尽量不用)最新代码，调拨单提交仓库时赋当前时间，历史使用姿势较多，存在不确定性',
  `expect_time` datetime DEFAULT NULL COMMENT '期望入库时间',
  `status` int(11) DEFAULT NULL COMMENT '调拨单状态：0-草稿，4-待出库，5-待入库，6-已入库，7-已关闭,8-待推单，9-待确认',
  `transport` int(11) DEFAULT NULL COMMENT '运输方式null:未知，0:快递，1:专车，2：干线',
  `addtime` datetime DEFAULT NULL COMMENT '创建时间',
  `create_admin_name` varchar(45) DEFAULT NULL COMMENT '单据创建人名称',
  `out_store_name` varchar(45) DEFAULT NULL COMMENT '出库仓名称，warehouse_storage_center表关联的warehouse_name的快照数据',
  `in_store_name` varchar(45) DEFAULT NULL COMMENT '入库存名称，warehouse_storage_center表关联的warehouse_name的快照数据',
  `updatetime` datetime DEFAULT NULL,
  `in_store_admin` int(11) DEFAULT NULL COMMENT '入库仓库负责人ID,warehouse_storage_center表关联的manage_admin_id的快照数据',
  `in_store_admin_name` varchar(45) DEFAULT NULL,
  `out_store_admin` int(11) DEFAULT NULL COMMENT '出库仓库负责人ID,warehouse_storage_center表关联的manage_admin_id的快照数据',
  `out_store_admin_name` varchar(45) DEFAULT NULL,
  `order_type` int(11) DEFAULT NULL COMMENT '单据类型:1:手动创建;2:自动创建;3:手动创建(自动计算);4:调拨计划创建',
  `expect_out_time` datetime DEFAULT NULL COMMENT '期望出库时间',
  `plan_list_no` varchar(20) DEFAULT NULL COMMENT '调拨计划单号,allocation_plan表的plan_list_no',
  `next_day_arrive` int(11) DEFAULT NULL COMMENT '是否次日达：0、是  1、不是 （空表示次日达）',
  `plan_list_id` bigint(20) DEFAULT NULL COMMENT '调拨计划id,allocation_plan表的id',
  `trunk_flag` int(11) DEFAULT NULL COMMENT '干线调度(0:否 1:是)',
  `storage_location` int(11) DEFAULT NULL COMMENT '货品的保存方式的组合，用于拆单。1:冷冻 2:冷藏 3:冷冻+冷藏 4:常温 5:常温+冷冻 6:冷藏+常温 7:冷冻+冷藏+常温',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户id(saas品牌方)',
  `out_warehouse_tenant_id` bigint(20) DEFAULT NULL COMMENT '调出仓的租户id，warehouse_storage_center表中的tenant_id字段',
  `in_warehouse_tenant_id` bigint(20) DEFAULT NULL COMMENT '调入仓的租户id,warehouse_storage_center表中的tenant_id字段',
  `phone` varchar(20) DEFAULT NULL COMMENT '创建人电话',
  `tenant_name` varchar(50) DEFAULT NULL COMMENT '租户品牌方名称',
  `manage_view_flag` int(11) DEFAULT NULL COMMENT '跨租户查询视图标记 值为1时为可被查询，逻辑为tenant_id不为2，且in/out warehouse_tenant_id 有一个为2',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `list_no_UNIQUE` (`list_no`) USING BTREE,
  KEY `idx_out_time` (`out_time`),
  KEY `idx_manage_view` (`status`,`manage_view_flag`),
  KEY `idx_except_out_time` (`status`,`expect_out_time`),
  KEY `idx_tenant` (`status`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='调拨单主表';