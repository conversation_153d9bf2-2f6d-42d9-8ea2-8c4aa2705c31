CREATE TABLE `merchant_leads` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `mname` varchar(45) NOT NULL COMMENT '商户名称',
  `province` varchar(20) DEFAULT NULL COMMENT '客户所在的省,  比如：广东、浙江、江苏',
  `city` varchar(20) DEFAULT NULL COMMENT '客户所在的市,  比如：广州市、深圳市、杭州市',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，关联area.area_no',
  `size` varchar(20) DEFAULT NULL COMMENT '客户的规模，单店或者大客户',
  `admin_id` int(11) DEFAULT NULL COMMENT '注册时的归属BD id，关联crm_bd_org.bd_id',
  `admin_name` varchar(45) DEFAULT NULL COMMENT '注册时的归属BD名字，关联crm_bd_org.bd_name',
  `status` int(11) DEFAULT '0' COMMENT '状态：0:待BD去跟进, 1:待门店注册, 2:门店已注册(代表本线索已被使用，不可被重复使用), 3:已失效',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP,
  `type` varchar(25) DEFAULT NULL COMMENT '经营类型，比如【面包蛋糕、茶饮、咖啡】等',
  `m_id` int(11) DEFAULT NULL COMMENT '客户id，关联`merchant`.`m_id`',
  `merchant_type` int(11) DEFAULT '0' COMMENT '0-鲜沐客户，1-pop客户(顺鹿达)',
  PRIMARY KEY (`id`),
  KEY `merchant_leads_status_admin_id_index` (`status`,`admin_id`),
  KEY `idx_status_m_id` (`status`,`m_id`),
  KEY `idx_merchant_leads` (`mname`)
) ENGINE=InnoDB AUTO_INCREMENT=327669 DEFAULT CHARSET=utf8 COMMENT '客户注册时所用到的线索,或者称作BD邀请码。如果客户有merchant_leads, 说明这个客户的注册方式是通过BD的地推码扫码注册的，注册时就属于BD的私海客户。反之，则说明门店是自主注册的。'