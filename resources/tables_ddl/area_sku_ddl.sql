CREATE TABLE `xianmudb`.`area_sku` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `sku` varchar(30) DEFAULT NULL COMMENT '商品SKU编码，关联inventory.sku，标识具体商品规格',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，关联area.area_no，标识商品销售的区域范围',
  `price` decimal(10,2) DEFAULT NULL COMMENT '商品在该运营服务区的销售价格(元)，用于区域差异化定价',
  `on_sale` tinyint(4) DEFAULT '0' COMMENT '商品上架销售状态：0=下架(客户不可见不可购买)、1=上架(客户可见可购买)',
  `m_type` int(11) NOT NULL DEFAULT '0' COMMENT '客户类型限制：0=普通商品(所有客户可购买)、1=大客户专享商品(仅特定大客户可购买)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_sku` (`sku`,`area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=2600900 DEFAULT CHARSET=utf8 COMMENT='运营服务区商品销售配置表，记录每个商品在各运营服务区的上架状态、销售价格和客户类型限制，用于区域化商品管理和差异化定价策略。';
