-- --------------------------------------------------
-- 表名：wms_stock_task_notice_order_detail
-- 中文名：出库通知单明细
-- 主要用途：记录每张出库通知单的商品明细，支持SKU级别的销售出库数据统计。
-- 典型应用：可与出库通知单主表联查，统计任意仓库、任意时间段的SKU销售出库明细。
-- --------------------------------------------------

CREATE TABLE `xianmudb`.`wms_stock_task_notice_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `notice_order_id` bigint(20) NOT NULL COMMENT '通知单ID，关联wms_stock_task_notice_order.id',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID，支持多租户隔离, 默认值为1,也就是只可查询鲜沐一个租户',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID，关联门店表：merchant.m_id',
  `out_order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '订单号，业务系统订单编号, orders.order_no的快照数据',
  `warehouse_no` int(11) NOT NULL COMMENT '库存仓编号，出库仓库唯一标识, warehouse_storage_center.warehouse_no的快照数据',
  `store_no` int(11) DEFAULT NULL COMMENT '城配仓编号，配送中心编号, warehouse_logistics_center.store_no的快照数据',
  `out_order_type` int(11) NOT NULL COMMENT '订单类型，参考orders.type的定义：0-普通（一次购买仅可一次性配送完），1-省心送（一次购买可分多次配送），2-运费，3-代下单，10-虚拟商品（黄金卡、充值等），11-直发采购，30-pop商城订单',
  `sku` varchar(64) DEFAULT '' COMMENT 'SKU编码，商品唯一标识，关联inventory.sku的定义',
  `goods_name` varchar(256) DEFAULT '' COMMENT '货品名称，products.pd_name的快照数据',
  `quantity` int(11) NOT NULL COMMENT '出库数量，单位：件',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否软删除：0-未删除，1-已删除，只需查询=0的数据即可',
  `customer_sku_code` varchar(64) DEFAULT NULL COMMENT '外部自有编码，客户自定义SKU',
  PRIMARY KEY (`id`),
  KEY `idx_notice_order_sku` (`notice_order_id`,`sku`)
) ENGINE=InnoDB AUTO_INCREMENT=9492787 DEFAULT CHARSET=utf8mb4 COMMENT='出库通知单明细表，记录每张出库通知单的商品明细，支撑SKU级别的销售出库数据统计与分析';

-- 索引说明：
-- 1. idx_notice_order_sku：支持按通知单和SKU高效查询，便于统计某单据下的商品明细。