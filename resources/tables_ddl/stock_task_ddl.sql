CREATE TABLE `xianmudb`.`stock_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务编号',
  `task_no` varchar(30) DEFAULT NULL COMMENT '任务订单编号',
  `area_no` int(11) DEFAULT NULL COMMENT '库存仓,warehouse_storage_center表中的warehouse_no字段',
  `type` int(11) DEFAULT '0' COMMENT '出入库类型：10-调拨入库、11-采购入库、12-退货入库、13-拒收回库、14-调拨未收入库（实收+拒收小于实发的情况）、15-盘盈入库、16-转换入库、17-终止调拨回库、19、新退货入库、20、缺货入库、30-批次调整、31-安全库存、50-调拨出库、51-销售出库、52-出样出库、53-货损出库、54-盘亏出库、55-转换出库、56-采购退货出库、57-补货出库、58-销售自提、59-调拨货损出库',
  `expect_time` datetime DEFAULT NULL COMMENT '预期出(入)库时间',
  `state` int(11) DEFAULT '0' COMMENT '出入库进展: 0待入(出)库 1部分入(出)库 2已入(出)库\n采购类型 01状态为正常 2 已完成 3 已取消 4已关闭',
  `addtime` datetime DEFAULT NULL COMMENT '添加时间',
  `admin_id` int(11) DEFAULT NULL COMMENT '发起人',
  `updatetime` datetime DEFAULT NULL,
  `out_store_no` int(11) DEFAULT NULL COMMENT '城配仓',
  `out_type` int(2) DEFAULT NULL COMMENT '出库性质，0普通 1越库',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `dimension` int(2) DEFAULT '0' COMMENT '盘点维度：0、SKU 1、类目 2、货位 3、批次,转换维度：0、库存转换 1、降级转换',
  `process_state` tinyint(4) DEFAULT '0' COMMENT '入库进度:0、全未入库1、部分入库2、完全入库',
  `mismatch_reason` varchar(100) DEFAULT NULL COMMENT '应入不符原因',
  `transition_field` int(11) DEFAULT NULL COMMENT '库存编号初始化过渡字段',
  `task_type` int(11) DEFAULT NULL COMMENT '任务类型 1、退货 2、拒收 3、拦截',
  `category` varchar(20) DEFAULT NULL COMMENT '类目名称',
  `close_reason` varchar(255) DEFAULT NULL COMMENT '关闭原因',
  `updater` varchar(36) DEFAULT NULL COMMENT '最后修改人admin_id',
  `option_flag` bigint(20) DEFAULT '0' COMMENT '扩展标志位',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID 1-鲜沐',
  `inventory_locked` tinyint(4) DEFAULT NULL COMMENT '库存冻结状态（0：未冻结，1：已冻结）',
  `system_source` int(11) DEFAULT '0' COMMENT '系统来源 0-内部 1-外部',
  `out_order_no` varchar(64) DEFAULT '' COMMENT '外部单号',
  `outbound_category` int(11) DEFAULT '0' COMMENT '出库分类 0-默认，1-POP出库 ，2POP T+2出库',
  `external_warehouse_no` varchar(32) DEFAULT NULL COMMENT '外部仓',
  PRIMARY KEY (`id`),
  KEY `task_no` (`task_no`) USING BTREE,
  KEY `expect_time_index` (`expect_time`) USING BTREE,
  KEY `type_index` (`type`,`area_no`),
  KEY `idx_area_type` (`area_no`,`state`,`task_no`),
  KEY `idx_area_no_state_type` (`area_no`,`state`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=730957 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='出入库任务表'
;