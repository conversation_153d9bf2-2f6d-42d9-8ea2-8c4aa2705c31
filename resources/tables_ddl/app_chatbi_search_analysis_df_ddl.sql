CREATE TABLE IF NOT EXISTS app_chatbi_search_analysis_df
(
    cust_id              STRING COMMENT '客户ID'
    ,cust_name           STRING COMMENT '客户名字'
    ,cust_type           STRING COMMENT '客户类型，比如烘焙、茶饮等'
    ,query_list          STRING COMMENT '客户历史搜索过的搜索词列表，以,分割，一定会以,结尾。比如：安佳,淡奶油,苹果,芒果,'
    ,sku_viewed          BIGINT COMMENT '客户总计搜索了多少SKU'
    ,sku_clicked         BIGINT COMMENT '客户总计点击了多少SKU'
    ,searched_date_range STRING COMMENT '客户有搜索的日期时间段'
    ,last_searched_date  STRING COMMENT '客户最后一次搜索日期'
)
COMMENT '客户的搜索分析汇总，以客户ID作为维度，可以用于查询“搜索过安佳淡奶油的客户”之类的问题。'
PARTITIONED BY 
(
    ds                   STRING COMMENT '分区日期'
)