
CREATE TABLE `xianmudb`.`stock_allocation_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sku` varchar(45) DEFAULT NULL COMMENT 'sku编号，关联inventory表的sku字段',
  `list_no` varchar(45) DEFAULT NULL COMMENT '调拨单号，关联stock_allocation_list表的list_no字段',
  `out_quantity` int(11) DEFAULT '0' COMMENT '调拨数量',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `addtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '变更时间',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称,products表的pd_name字段快照数据',
  `weight` varchar(100) DEFAULT NULL COMMENT '规格,inventory表的weight的快照数据',
  `allocation_lock_quantity` int(11) DEFAULT '0' COMMENT '调拨冻结数量，用于记录当前冻结库存情况，方便后续操作释放货冻结库存',
  `operate_remark` varchar(255) DEFAULT NULL COMMENT '运营备注',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户id(saas品牌方)',
  `actual_out_quantity` int(11) DEFAULT '0' COMMENT '实际出库数量',
  `actual_in_quantity` int(11) DEFAULT '0' COMMENT '实际入库数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_list_no_sku` (`list_no`,`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='调拨单详单表，通过list_no与调拨单主表关联';
