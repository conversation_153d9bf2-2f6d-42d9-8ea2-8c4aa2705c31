CREATE TABLE `xianmudb`.`merchant_label` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `name` varchar(255) NOT NULL COMMENT '标签名称, 例如：过年营业、过年不营业、校区商户、非校区商户等',
  `status` tinyint(4) DEFAULT '1' COMMENT '售后状态 0-禁用 1-启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=718 DEFAULT CHARSET=utf8 COMMENT='客户标签表, 例如：过年营业、过年不营业、校区商户、非校区商户等'

CREATE TABLE `xianmudb`.`merchant_label_correlation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `label_id` bigint(20) unsigned DEFAULT NULL COMMENT '商户标签ID，对应merchant_label.id',
  `m_id` bigint(20) unsigned DEFAULT NULL COMMENT '商户ID，对应merchant.m_id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_index` (`label_id`,`m_id`) COMMENT '唯一索引',
  KEY `merchant_id_index` (`m_id`) COMMENT '商户索引'
) ENGINE=InnoDB AUTO_INCREMENT=360914 DEFAULT CHARSET=utf8 COMMENT='商户标签关联表，记录商户与标签之间的关联关系。';