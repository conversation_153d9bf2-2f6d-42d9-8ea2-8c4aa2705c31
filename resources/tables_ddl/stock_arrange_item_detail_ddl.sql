CREATE TABLE `xianmudb`.`stock_arrange_item_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `sku` varchar(30) DEFAULT NULL COMMENT 'SKU编码，关联商品SKU表:inventory.sku',
  `stock_arrange_item_id` int(11) DEFAULT NULL COMMENT '预约单条目ID，关联入库预约单品维度详单表:stock_arrange_item.id',
  `stock_arrange_id` int(11) DEFAULT NULL COMMENT '预约单ID，关联入库预约单表:stock_arrange.id',
  `stock_task_id` int(11) DEFAULT NULL COMMENT '任务ID，关联库存任务表:stock_task.id',
  `arr_quantity` int(10) unsigned DEFAULT '0' COMMENT '预约数量',
  `quantity` int(11) DEFAULT '0' COMMENT '实际到货数量',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `quality_date` date DEFAULT NULL COMMENT '保质期（到期日）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `updater` varchar(255) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`),
  KEY `idx_stock_task_id` (`stock_task_id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_item_id` (`stock_arrange_item_id`),
  KEY `idx_stockarrangeid_sku` (`stock_arrange_id`,`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购预约单的入库明细，入库的商品及其有效期维度详单，记录采购预约单中每个SKU按生产日期和保质期的详细信息，即SKU+生产日期为唯一键';
