CREATE TABLE `xianmudb`.`fence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fence_name` varchar(50) DEFAULT NULL COMMENT '围栏名称',
  `store_no` int(11) DEFAULT NULL COMMENT '城配仓，对应warehouse_inventory_mapping',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，对应area.area_no',
  `status` int(11) DEFAULT NULL COMMENT '状态 0正常 1失效，一般都取正常的',
  `order_channel_type` varchar(255) NOT NULL DEFAULT '1000,2000,3000' COMMENT '下单渠道类型 1000鲜沐平台客户 2000鲜沐大客户 3000 Saas客户',
  PRIMARY KEY (`id`),
  KEY `fence_area_index` (`area_no`),
  KEY `idx_fence` (`store_no`,`area_no`,`type`,`status`),
  KEY `idx_status` (`status`,`area_no`,`store_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1194 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='围栏表'
;