#!/usr/bin/env python3
"""
数据库连接池状态检查脚本

快速检查数据库连接池的当前状态，用于诊断连接池问题。
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.db_pool_diagnostics import diagnose_connection_pools, test_pool_connections
from src.utils.logger import logger


def print_pool_status():
    """打印连接池状态"""
    print("=" * 60)
    print(f"数据库连接池状态检查 - {datetime.now()}")
    print("=" * 60)
    
    try:
        # 获取连接池诊断信息
        diagnosis = diagnose_connection_pools()
        
        print("\n📊 连接池状态:")
        print("-" * 40)
        
        for pool_name, pool_info in diagnosis["current_status"]["pools"].items():
            print(f"\n🔗 {pool_name.upper()} 连接池:")
            print(f"   状态: {pool_info.get('status', 'unknown')}")
            print(f"   池大小: {pool_info.get('pool_size', 'unknown')}")
            
            available = pool_info.get('available_connections', 'unknown')
            active = pool_info.get('active_connections', 'unknown')
            utilization = pool_info.get('utilization_rate', 'unknown')
            
            print(f"   可用连接: {available}")
            print(f"   活跃连接: {active}")
            
            if isinstance(utilization, (int, float)):
                print(f"   使用率: {utilization:.2%}")
                
                # 状态指示
                if utilization > 0.9:
                    print("   ⚠️  使用率过高！")
                elif utilization > 0.8:
                    print("   ⚡ 使用率较高")
                elif utilization < 0.1:
                    print("   💤 使用率很低")
                else:
                    print("   ✅ 使用率正常")
            else:
                print(f"   使用率: {utilization}")
            
            # 显示配置信息（如果有）
            if 'config' in pool_info:
                config = pool_info['config']
                print(f"   配置: {config.get('host')}:{config.get('port')}/{config.get('database')}")
        
        # 显示建议
        if diagnosis.get("recommendations"):
            print("\n💡 建议:")
            print("-" * 40)
            for i, recommendation in enumerate(diagnosis["recommendations"], 1):
                print(f"   {i}. {recommendation}")
        
        print(f"\n📈 监控状态: {'运行中' if diagnosis['monitoring_active'] else '未运行'}")
        print(f"📚 历史记录: {diagnosis['history_count']} 条")
        
    except Exception as e:
        print(f"❌ 获取连接池状态失败: {e}")
        logger.error(f"获取连接池状态失败: {e}", exc_info=True)


def test_connections():
    """测试连接获取"""
    print("\n🧪 连接测试:")
    print("-" * 40)
    
    pools_to_test = ["chatbi", "business"]
    
    for pool_name in pools_to_test:
        print(f"\n测试 {pool_name.upper()} 连接池...")
        
        try:
            result = test_pool_connections(pool_name, count=3)
            
            success_rate = result["successful_acquisitions"] / result["test_count"] * 100
            print(f"   成功率: {success_rate:.1f}% ({result['successful_acquisitions']}/{result['test_count']})")
            
            if result.get("avg_acquisition_time"):
                print(f"   平均获取时间: {result['avg_acquisition_time']:.4f}秒")
            
            if result["errors"]:
                print(f"   错误: {len(result['errors'])} 个")
                for error in result["errors"][:2]:  # 只显示前2个错误
                    print(f"     - {error}")
            
            if success_rate == 100:
                print("   ✅ 连接测试通过")
            else:
                print("   ⚠️  连接测试有问题")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")


def main():
    """主函数"""
    # 检查命令行参数
    test_connections_flag = "--test" in sys.argv or "-t" in sys.argv
    json_output = "--json" in sys.argv
    
    if json_output:
        # JSON格式输出
        try:
            diagnosis = diagnose_connection_pools()
            print(json.dumps(diagnosis, indent=2, default=str))
        except Exception as e:
            print(json.dumps({"error": str(e)}, indent=2))
        return
    
    # 常规输出
    print_pool_status()
    
    if test_connections_flag:
        test_connections()
    
    print("\n" + "=" * 60)
    print("检查完成")
    
    if not test_connections_flag:
        print("\n💡 使用 --test 参数进行连接测试")
        print("💡 使用 --json 参数获取JSON格式输出")


if __name__ == "__main__":
    main()
