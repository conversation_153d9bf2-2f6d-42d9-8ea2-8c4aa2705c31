#!/usr/bin/env python3
"""
离线推荐服务修复测试脚本

用于测试修复后的离线推荐服务是否正常工作，以及连接池是否正确释放。
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.recommendation.offline_recommendation_service import OfflineRecommendationService
from src.utils.db_pool_diagnostics import db_diagnostics, diagnose_connection_pools, test_pool_connections
from src.utils.logger import logger


def test_database_field_fix():
    """测试数据库字段修复"""
    print("\n=== 测试数据库字段修复 ===")
    
    service = OfflineRecommendationService()
    test_user_email = "<EMAIL>"
    test_user_open_id = "test_open_id_123"
    
    try:
        # 测试更新生成状态（之前会因为recommendations字段报错）
        print("测试更新生成状态为失败...")
        service._update_generation_status(
            test_user_email, 
            test_user_open_id, 
            status=3,  # 失败状态
            error_message="测试错误消息"
        )
        print("✅ 更新失败状态成功")
        
        # 测试更新生成状态为完成
        print("测试更新生成状态为完成...")
        service._update_generation_status(
            test_user_email, 
            test_user_open_id, 
            status=2  # 完成状态
        )
        print("✅ 更新完成状态成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库字段修复测试失败: {e}")
        return False


def test_connection_pool_management():
    """测试连接池管理"""
    print("\n=== 测试连接池管理 ===")
    
    # 获取初始连接池状态
    initial_stats = diagnose_connection_pools()
    print(f"初始连接池状态:")
    for pool_name, pool_info in initial_stats["current_status"]["pools"].items():
        available = pool_info.get("available_connections", "unknown")
        active = pool_info.get("active_connections", "unknown")
        print(f"  {pool_name}: 可用={available}, 活跃={active}")
    
    # 测试连接获取和释放
    print("\n测试连接获取和释放...")
    test_results = test_pool_connections("chatbi", count=10)
    
    print(f"连接测试结果:")
    print(f"  成功获取: {test_results['successful_acquisitions']}")
    print(f"  获取失败: {test_results['failed_acquisitions']}")
    if test_results.get("avg_acquisition_time"):
        print(f"  平均获取时间: {test_results['avg_acquisition_time']:.4f}秒")
    
    if test_results["errors"]:
        print("  错误信息:")
        for error in test_results["errors"][:3]:  # 只显示前3个错误
            print(f"    - {error}")
    
    # 获取测试后连接池状态
    final_stats = diagnose_connection_pools()
    print(f"\n测试后连接池状态:")
    for pool_name, pool_info in final_stats["current_status"]["pools"].items():
        available = pool_info.get("available_connections", "unknown")
        active = pool_info.get("active_connections", "unknown")
        print(f"  {pool_name}: 可用={available}, 活跃={active}")
    
    # 检查是否有连接泄漏
    success = test_results["successful_acquisitions"] == 10 and test_results["failed_acquisitions"] == 0
    if success:
        print("✅ 连接池管理测试成功")
    else:
        print("❌ 连接池管理测试失败")
    
    return success


def test_offline_recommendation_service():
    """测试离线推荐服务完整流程"""
    print("\n=== 测试离线推荐服务完整流程 ===")
    
    service = OfflineRecommendationService()
    
    try:
        # 测试获取活跃用户（限制数量避免过多处理）
        print("测试获取活跃用户...")
        active_users = service._get_active_users(days=7)  # 只获取最近7天的
        print(f"获取到 {len(active_users)} 个活跃用户")
        
        if not active_users:
            print("⚠️  没有活跃用户，跳过推荐生成测试")
            return True
        
        # 只测试第一个用户，避免过多AI调用
        test_user = active_users[0]
        print(f"为用户 {test_user['email']} 生成推荐...")
        
        # 测试推荐生成
        service._generate_recommendations_for_user(test_user)
        print("✅ 推荐生成完成")
        
        # 测试获取离线推荐
        print("测试获取离线推荐...")
        recommendations = service.get_offline_recommendations(test_user['email'], count=3)
        print(f"获取到 {len(recommendations)} 条推荐:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        
        print("✅ 离线推荐服务测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 离线推荐服务测试失败: {e}")
        logger.error(f"离线推荐服务测试失败: {e}", exc_info=True)
        return False


def test_recommendations_storage():
    """测试推荐存储功能"""
    print("\n=== 测试推荐存储功能 ===")
    
    service = OfflineRecommendationService()
    test_user_email = "<EMAIL>"
    test_user_open_id = "test_storage_123"
    
    test_recommendations = [
        "测试推荐问题1：最近7天的销售数据如何？",
        "测试推荐问题2：哪些商品库存不足？",
        "测试推荐问题3：客户满意度调查结果？"
    ]
    
    try:
        # 测试存储推荐
        print("测试存储推荐...")
        service._store_recommendations(test_user_email, test_user_open_id, test_recommendations)
        print("✅ 推荐存储成功")
        
        # 测试获取推荐
        print("测试获取存储的推荐...")
        retrieved_recommendations = service.get_offline_recommendations(test_user_email, count=5)
        print(f"获取到 {len(retrieved_recommendations)} 条推荐")
        
        if len(retrieved_recommendations) == len(test_recommendations):
            print("✅ 推荐存储和获取测试成功")
            return True
        else:
            print(f"❌ 推荐数量不匹配，期望 {len(test_recommendations)}，实际 {len(retrieved_recommendations)}")
            return False
            
    except Exception as e:
        print(f"❌ 推荐存储测试失败: {e}")
        logger.error(f"推荐存储测试失败: {e}", exc_info=True)
        return False


def main():
    """主测试函数"""
    print("开始测试离线推荐服务修复...")
    print(f"测试时间: {datetime.now()}")
    
    # 开始连接池监控
    db_diagnostics.start_monitoring(interval_seconds=10)
    
    test_results = []
    
    try:
        # 1. 测试数据库字段修复
        test_results.append(("数据库字段修复", test_database_field_fix()))
        
        # 2. 测试连接池管理
        test_results.append(("连接池管理", test_connection_pool_management()))
        
        # 3. 测试推荐存储功能
        test_results.append(("推荐存储功能", test_recommendations_storage()))
        
        # 4. 测试完整的离线推荐服务（可选，因为会调用AI）
        if len(sys.argv) > 1 and sys.argv[1] == "--full":
            test_results.append(("完整推荐服务", test_offline_recommendation_service()))
        else:
            print("\n⚠️  跳过完整推荐服务测试（使用 --full 参数启用）")
        
    finally:
        # 停止监控
        db_diagnostics.stop_monitoring()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！离线推荐服务修复成功。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查日志获取详细信息。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
