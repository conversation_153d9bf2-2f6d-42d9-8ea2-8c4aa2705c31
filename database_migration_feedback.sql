-- 反馈收集功能数据库迁移脚本
-- 兼容 MySQL 5.6，使用 TEXT 字段存储 JSON 格式的标签数据
-- 执行前请备份数据库

USE chatbi;

-- 扩展 bad_case 表，添加反馈相关字段
ALTER TABLE bad_case 
ADD COLUMN feedback_tags TEXT NULL COMMENT '选择的反馈标签(JSON格式字符串)',
ADD COLUMN custom_feedback TEXT NULL COMMENT '用户自定义反馈文本',
ADD COLUMN feedback_submitted_at TIMESTAMP NULL COMMENT '反馈提交时间';

-- 扩展 good_case 表，添加反馈相关字段  
ALTER TABLE good_case 
ADD COLUMN feedback_tags TEXT NULL COMMENT '选择的反馈标签(JSON格式字符串)',
ADD COLUMN custom_feedback TEXT NULL COMMENT '用户自定义反馈文本',
ADD COLUMN feedback_submitted_at TIMESTAMP NULL COMMENT '反馈提交时间';

-- 验证表结构
DESCRIBE bad_case;
DESCRIBE good_case;

-- 示例数据格式说明
-- feedback_tags 字段存储格式示例：
-- 正面反馈: '["准确", "清晰", "有用"]'
-- 负面反馈: '["不准确", "不清晰", "偏题"]'
-- 空值表示未提供标签反馈

-- custom_feedback 字段存储用户输入的文本反馈
-- feedback_submitted_at 记录反馈提交的时间戳，NULL表示未提交反馈
