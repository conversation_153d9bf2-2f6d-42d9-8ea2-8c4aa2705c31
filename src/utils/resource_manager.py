"""
Simple resource manager for loading files from directories.
"""
import os
from pathlib import Path
from typing import Optional, List

from src.utils.logger import logger

# Get the project root directory
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))


def load_resource(directory: str, filename: str, encoding: str = 'utf-8') -> Optional[str]:
    """
    Load a resource file from a specified directory.

    Args:
        directory: The directory name (relative to project root)
        filename: The name of the resource file
        encoding: The encoding to use when reading the file

    Returns:
        str: The content of the resource file, or None if the file doesn't exist
    """
    # Try the specified directory first
    resource_path = PROJECT_ROOT / 'resources' / directory / filename

    try:
        with open(resource_path, 'r', encoding=encoding) as f:
            return f.read()
    except FileNotFoundError:
        # Try some common fallback locations
        fallback_paths = [
            PROJECT_ROOT / filename,  # Root directory
            PROJECT_ROOT / directory / filename,  # Direct subdirectory
            PROJECT_ROOT / "src" / "services" / "agent" / filename,  # Old agent directory
        ]

        for path in fallback_paths:
            try:
                with open(path, 'r', encoding=encoding) as f:
                    content = f.read()
                    logger.warning(f"Resource {filename} loaded from fallback path: {path}")
                    return content
            except FileNotFoundError:
                continue

        logger.error(f"Resource not found: {filename} in directory {directory}")
        return None


def list_resources(directory: str, file_extension: str = None) -> List[str]:
    """
    List all files in a specified directory with optional file extension filter.

    Args:
        directory: The directory name (relative to project root)
        file_extension: Optional file extension filter (e.g., '.yml', '.txt')

    Returns:
        List[str]: List of filenames in the directory, or empty list if directory doesn't exist
    """
    # Try the resources directory first
    resource_dir = PROJECT_ROOT / 'resources' / directory

    try:
        if resource_dir.is_dir():
            if file_extension:
                files = [f.name for f in resource_dir.iterdir() if f.is_file() and f.name.endswith(file_extension)]
            else:
                files = [f.name for f in resource_dir.iterdir() if f.is_file()]
            logger.info(f"Found {len(files)} files in resources/{directory}")
            return files
    except Exception as e:
        logger.warning(f"Error listing resources in resources/{directory}: {e}")

    # Try fallback locations
    fallback_dirs = [
        PROJECT_ROOT / directory,  # Direct subdirectory
        PROJECT_ROOT / "src" / "services" / "agent" / directory,  # Old agent directory
    ]

    for path in fallback_dirs:
        try:
            if path.is_dir():
                if file_extension:
                    files = [f.name for f in path.iterdir() if f.is_file() and f.name.endswith(file_extension)]
                else:
                    files = [f.name for f in path.iterdir() if f.is_file()]
                logger.warning(f"Found {len(files)} files in fallback path: {path}")
                return files
        except Exception as e:
            logger.warning(f"Error listing resources in fallback path {path}: {e}")

    logger.error(f"Directory not found: {directory}")
    return []
