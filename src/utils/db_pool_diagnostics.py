"""
数据库连接池诊断工具

用于监控和诊断MySQL连接池的使用情况，帮助发现连接泄漏问题。
"""

import time
import threading
from typing import Dict, Any, List
from src.utils.logger import logger
from src.db.connection import chatbi_db_pool, business_db_pool, logical_dw_db_pool


class ConnectionPoolDiagnostics:
    """连接池诊断工具"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.stats_history = []
        self.max_history = 50
        
    def get_pool_detailed_stats(self) -> Dict[str, Any]:
        """获取详细的连接池统计信息"""
        stats = {
            "timestamp": time.time(),
            "pools": {}
        }
        
        # 检查ChatBI连接池
        if chatbi_db_pool:
            stats["pools"]["chatbi"] = self._analyze_pool(chatbi_db_pool, "ChatBI")
        
        # 检查Business连接池
        if business_db_pool:
            stats["pools"]["business"] = self._analyze_pool(business_db_pool, "Business")
            
        # 检查LogicalDW连接池
        if logical_dw_db_pool:
            stats["pools"]["logical_dw"] = self._analyze_pool(logical_dw_db_pool, "LogicalDW")
        
        return stats
    
    def _analyze_pool(self, pool, pool_name: str) -> Dict[str, Any]:
        """分析单个连接池的状态"""
        try:
            pool_info = {
                "name": pool_name,
                "status": "active",
                "pool_size": pool.pool_size,
                "pool_name_attr": getattr(pool, 'pool_name', 'unknown'),
            }
            
            # 尝试获取连接队列信息（MySQL Connector特定）
            if hasattr(pool, '_cnx_queue'):
                queue_size = pool._cnx_queue.qsize()
                pool_info.update({
                    "available_connections": queue_size,
                    "active_connections": pool.pool_size - queue_size,
                    "utilization_rate": (pool.pool_size - queue_size) / pool.pool_size,
                    "queue_size": queue_size
                })
            else:
                # 如果无法获取队列信息，尝试其他方式
                pool_info.update({
                    "available_connections": "unknown",
                    "active_connections": "unknown", 
                    "utilization_rate": "unknown",
                    "note": "无法获取详细连接信息"
                })
            
            # 尝试获取连接池配置信息
            if hasattr(pool, '_config'):
                config = pool._config
                pool_info["config"] = {
                    "host": config.get('host', 'unknown'),
                    "port": config.get('port', 'unknown'),
                    "database": config.get('database', 'unknown'),
                    "user": config.get('user', 'unknown')
                }
            
            return pool_info
            
        except Exception as e:
            logger.error(f"分析连接池 {pool_name} 时出错: {e}")
            return {
                "name": pool_name,
                "status": "error",
                "error": str(e),
                "pool_size": 0,
                "available_connections": 0,
                "active_connections": 0
            }
    
    def test_connection_acquisition(self, pool_name: str = "chatbi", count: int = 5) -> Dict[str, Any]:
        """测试连接获取和释放"""
        from src.db.connection import get_db_connection
        from src.db.database_enum import Database
        
        # 根据池名选择数据库
        if pool_name.lower() == "chatbi":
            database = Database.CHATBI
        elif pool_name.lower() == "business":
            database = Database.BUSINESS
        elif pool_name.lower() == "logical_dw":
            database = Database.LOGICAL_DW
        else:
            return {"error": f"未知的连接池名称: {pool_name}"}
        
        results = {
            "pool_name": pool_name,
            "test_count": count,
            "successful_acquisitions": 0,
            "failed_acquisitions": 0,
            "acquisition_times": [],
            "errors": []
        }
        
        for i in range(count):
            start_time = time.time()
            conn = None
            try:
                conn = get_db_connection(database)
                if conn and conn.is_connected():
                    results["successful_acquisitions"] += 1
                    acquisition_time = time.time() - start_time
                    results["acquisition_times"].append(acquisition_time)
                    
                    # 测试简单查询
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                else:
                    results["failed_acquisitions"] += 1
                    results["errors"].append(f"Test {i+1}: 连接无效")
                    
            except Exception as e:
                results["failed_acquisitions"] += 1
                results["errors"].append(f"Test {i+1}: {str(e)}")
            finally:
                if conn and conn.is_connected():
                    try:
                        conn.close()
                    except Exception as close_error:
                        results["errors"].append(f"Test {i+1} 关闭连接失败: {str(close_error)}")
        
        # 计算统计信息
        if results["acquisition_times"]:
            results["avg_acquisition_time"] = sum(results["acquisition_times"]) / len(results["acquisition_times"])
            results["max_acquisition_time"] = max(results["acquisition_times"])
            results["min_acquisition_time"] = min(results["acquisition_times"])
        
        return results
    
    def start_monitoring(self, interval_seconds: int = 30):
        """开始监控连接池"""
        if self.monitoring:
            logger.warning("连接池监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval_seconds,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"开始监控连接池，间隔: {interval_seconds}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("连接池监控已停止")
    
    def _monitor_loop(self, interval_seconds: int):
        """监控循环"""
        while self.monitoring:
            try:
                stats = self.get_pool_detailed_stats()
                self._record_stats(stats)
                self._check_pool_health(stats)
                time.sleep(interval_seconds)
            except Exception as e:
                logger.error(f"连接池监控出错: {e}", exc_info=True)
                time.sleep(interval_seconds)
    
    def _record_stats(self, stats: Dict[str, Any]):
        """记录统计信息"""
        self.stats_history.append(stats)
        if len(self.stats_history) > self.max_history:
            self.stats_history.pop(0)
    
    def _check_pool_health(self, stats: Dict[str, Any]):
        """检查连接池健康状况"""
        for pool_name, pool_stats in stats["pools"].items():
            if pool_stats.get("status") != "active":
                logger.error(f"连接池 {pool_name} 状态异常: {pool_stats.get('status')}")
                continue
            
            utilization = pool_stats.get("utilization_rate")
            if isinstance(utilization, (int, float)):
                if utilization > 0.9:
                    logger.warning(f"连接池 {pool_name} 使用率过高: {utilization:.2%}")
                elif utilization > 0.8:
                    logger.info(f"连接池 {pool_name} 使用率较高: {utilization:.2%}")
            
            available = pool_stats.get("available_connections")
            if isinstance(available, int) and available <= 1:
                logger.warning(f"连接池 {pool_name} 可用连接数过低: {available}")
    
    def get_summary_report(self) -> Dict[str, Any]:
        """获取汇总报告"""
        current_stats = self.get_pool_detailed_stats()
        
        report = {
            "current_status": current_stats,
            "monitoring_active": self.monitoring,
            "history_count": len(self.stats_history),
            "recommendations": []
        }
        
        # 分析历史数据并给出建议
        for pool_name, pool_stats in current_stats["pools"].items():
            utilization = pool_stats.get("utilization_rate")
            if isinstance(utilization, (int, float)):
                if utilization > 0.8:
                    report["recommendations"].append(
                        f"连接池 {pool_name} 使用率较高({utilization:.2%})，建议检查是否有连接泄漏"
                    )
                elif utilization < 0.1:
                    report["recommendations"].append(
                        f"连接池 {pool_name} 使用率很低({utilization:.2%})，可以考虑减少池大小"
                    )
        
        return report


# 全局诊断实例
db_diagnostics = ConnectionPoolDiagnostics()


def diagnose_connection_pools() -> Dict[str, Any]:
    """快速诊断所有连接池"""
    return db_diagnostics.get_summary_report()


def test_pool_connections(pool_name: str = "chatbi", count: int = 5) -> Dict[str, Any]:
    """测试连接池连接获取"""
    return db_diagnostics.test_connection_acquisition(pool_name, count)
