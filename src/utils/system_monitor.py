"""
系统资源监控器模块
负责监控系统资源，防止文件描述符泄漏
"""
import os
import threading
import time
from src.utils.logger import logger

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available, resource monitoring will be limited")


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, check_interval=60):
        self.check_interval = check_interval
        self.monitoring = False
        self.monitor_thread = None
        
        if PSUTIL_AVAILABLE:
            self.process = psutil.Process()
        else:
            self.process = None
        
    def start_monitoring(self):
        """开始监控资源使用情况"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("System resource monitoring started")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("System resource monitoring stopped")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_resources()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
                time.sleep(self.check_interval)
    
    def _check_resources(self):
        """检查资源使用情况"""
        if not PSUTIL_AVAILABLE or not self.process:
            return
            
        try:
            # 获取文件描述符数量
            num_fds = self.process.num_fds()
            
            # 获取系统限制
            soft_limit, hard_limit = self.process.rlimit(psutil.RLIMIT_NOFILE)
            
            # 计算使用率
            usage_percent = (num_fds / soft_limit) * 100
            
            # 记录资源使用情况
            logger.debug(f"File descriptors: {num_fds}/{soft_limit} ({usage_percent:.1f}%)")
            
            # 如果使用率过高，发出警告
            if usage_percent > 80:
                logger.warning(
                    f"High file descriptor usage: {num_fds}/{soft_limit} ({usage_percent:.1f}%)"
                )
                self._log_open_files()
            
            # 如果使用率超过90%，触发紧急清理
            if usage_percent > 90:
                logger.error(
                    f"Critical file descriptor usage: {num_fds}/{soft_limit} ({usage_percent:.1f}%)"
                )
                self._emergency_cleanup()
                
        except Exception as e:
            logger.error(f"Failed to check resources: {e}")
    
    def _log_open_files(self):
        """记录打开的文件信息"""
        if not PSUTIL_AVAILABLE or not self.process:
            return
            
        try:
            open_files = self.process.open_files()
            logger.warning(f"Open files count: {len(open_files)}")
            
            # 按类型统计
            file_types = {}
            for file_info in open_files:
                path = file_info.path
                if 'socket:' in path:
                    file_types['socket'] = file_types.get('socket', 0) + 1
                elif 'pipe:' in path:
                    file_types['pipe'] = file_types.get('pipe', 0) + 1
                else:
                    file_types['file'] = file_types.get('file', 0) + 1
            
            logger.warning(f"File types: {file_types}")
            
        except Exception as e:
            logger.error(f"Failed to get open files info: {e}")
    
    def _emergency_cleanup(self):
        """紧急清理资源"""
        logger.warning("Triggering emergency resource cleanup...")
        
        # 触发垃圾回收
        import gc
        gc.collect()
        
        logger.warning("Emergency cleanup completed")
    
    def get_current_usage(self):
        """获取当前资源使用情况"""
        if not PSUTIL_AVAILABLE or not self.process:
            return None
            
        try:
            num_fds = self.process.num_fds()
            soft_limit, hard_limit = self.process.rlimit(psutil.RLIMIT_NOFILE)
            usage_percent = (num_fds / soft_limit) * 100
            
            return {
                'file_descriptors': num_fds,
                'soft_limit': soft_limit,
                'hard_limit': hard_limit,
                'usage_percent': usage_percent
            }
        except Exception as e:
            logger.error(f"Failed to get resource usage: {e}")
            return None


# 全局系统监控器实例
system_monitor = SystemMonitor()


def start_system_monitoring():
    """启动系统监控"""
    system_monitor.start_monitoring()


def stop_system_monitoring():
    """停止系统监控"""
    system_monitor.stop_monitoring()


def get_system_usage():
    """获取当前系统资源使用情况"""
    return system_monitor.get_current_usage()


def log_current_usage():
    """记录当前资源使用情况"""
    usage = get_system_usage()
    if usage:
        logger.info(
            f"Current resource usage - FDs: {usage['file_descriptors']}/{usage['soft_limit']} "
            f"({usage['usage_percent']:.1f}%)"
        )
    else:
        logger.info("Resource usage information not available")
