import time
import functools
from typing import Callable, Any, Dict

# 全局缓存字典，用于存储不同函数的缓存
# 结构: { 'function_qualname': { 'cache_key': (timestamp, value) } }
_caches: Dict[str, Dict[Any, tuple[float, Any]]] = {}

# 锁，用于线程安全，如果将来需要在多线程环境中使用
# from threading import Lock
# _locks: Dict[str, Lock] = {}

def in_memory_cache(expire_seconds: int):
    """
    一个内存缓存装饰器，支持设置过期时间。

    Args:
        expire_seconds (int): 缓存的过期时间（秒）。
    """
    def decorator(func: Callable) -> Callable:
        func_qualname = func.__qualname__ # 使用函数的限定名作为缓存的顶级键
        if func_qualname not in _caches:
            _caches[func_qualname] = {}
            # _locks[func_qualname] = Lock() # 为每个函数创建一个锁

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 创建缓存键，基于函数参数
            # 注意：kwargs中的顺序可能影响键，因此我们对其进行排序
            # 同时，确保所有参数都是可哈希的
            try:
                key_parts = [args]
                if kwargs:
                    key_parts.append(tuple(sorted(kwargs.items())))
                cache_key = tuple(key_parts)
            except TypeError:
                # 如果参数不可哈希，则不使用缓存，直接调用函数
                # 或者可以记录一个警告
                print(f"警告: 函数 {func_qualname} 的参数不可哈希，无法使用缓存。")
                return func(*args, **kwargs)

            current_time = time.time()
            cache_for_func = _caches[func_qualname]
            # lock_for_func = _locks[func_qualname]

            # with lock_for_func: # 考虑线程安全
            if cache_key in cache_for_func:
                cached_time, cached_value = cache_for_func[cache_key]
                if current_time - cached_time < expire_seconds:
                    # 缓存有效
                    print(f"命中缓存: {func_qualname} (key: {str(cache_key)[:100]}...)")
                    return cached_value
                else:
                    # 缓存过期
                    print(f"缓存过期: {func_qualname} (key: {str(cache_key)[:100]}...)")
                    del cache_for_func[cache_key]

            # 缓存未命中或已过期
            print(f"缓存未命中: {func_qualname} (key: {str(cache_key)[:100]}...)")
            result = func(*args, **kwargs)
            # with lock_for_func: # 考虑线程安全
            cache_for_func[cache_key] = (current_time, result)
            return result
        return wrapper
    return decorator

def clear_cache(func_qualname: str = None, cache_key: Any = None) -> None:
    """
    清除指定函数或特定键的缓存。

    Args:
        func_qualname (str, optional): 要清除缓存的函数的限定名。
                                     如果为 None，则清除所有缓存。
        cache_key (Any, optional): 要清除的特定缓存键。
                                  仅当 func_qualname 被指定时有效。
                                  如果为 None，则清除该函数的所有缓存。
    """
    # with Lock(): # 全局锁，或者更细粒度的锁
    if func_qualname is None:
        _caches.clear()
        print("所有内存缓存已清除。")
    elif func_qualname in _caches:
        if cache_key is None:
            _caches[func_qualname].clear()
            print(f"函数 {func_qualname} 的所有缓存已清除。")
        elif cache_key in _caches[func_qualname]:
            del _caches[func_qualname][cache_key]
            print(f"函数 {func_qualname} 的键 {str(cache_key)[:100]}... 的缓存已清除。")
        else:
            print(f"函数 {func_qualname} 中未找到键 {str(cache_key)[:100]}... 的缓存。")
    else:
        print(f"未找到函数 {func_qualname} 的缓存。")

if __name__ == '__main__':
    # 示例用法
    @in_memory_cache(expire_seconds=5)
    def expensive_calculation(a: int, b: int, c: str = "default") -> int:
        print(f"执行昂贵的计算: {a}, {b}, {c}...")
        time.sleep(2) # 模拟耗时操作
        return a + b

    print("第一次调用:")
    print(f"结果: {expensive_calculation(1, 2)}")
    print(f"结果: {expensive_calculation(1, 2, c='test')}")


    print("\n第二次调用 (应命中缓存):")
    print(f"结果: {expensive_calculation(1, 2)}")
    print(f"结果: {expensive_calculation(1, 2, c='test')}")

    print("\n等待缓存过期 (等待6秒)...")
    time.sleep(6)

    print("\n第三次调用 (缓存应已过期):")
    print(f"结果: {expensive_calculation(1, 2)}")

    print("\n清除特定缓存:")
    expensive_calculation(10, 20) # 缓存 (10,20)
    clear_cache(expensive_calculation.__qualname__, ((10, 20), (('c', 'default'),))) # 注意key的构造
    print(f"结果: {expensive_calculation(10, 20)}") # 应该重新计算

    print("\n清除函数所有缓存:")
    expensive_calculation(5,5)
    clear_cache(expensive_calculation.__qualname__)
    print(f"结果: {expensive_calculation(5, 5)}") # 应该重新计算

    print("\n测试不可哈希参数:")
    @in_memory_cache(expire_seconds=5)
    def func_with_list(data: list):
        print(f"使用列表参数调用: {data}")
        return sum(data)

    my_list = [1,2,3]
    print(f"结果: {func_with_list(my_list)}")
    print(f"结果: {func_with_list(my_list)}") # 不会命中缓存

    print("\n测试清除所有缓存:")
    expensive_calculation(1,1)
    func_with_list([1,1])
    clear_cache()
    expensive_calculation(1,1)
    func_with_list([1,1])
