"""
Good case repository module.

This module provides database operations for managing good cases.
"""

import json
from datetime import datetime
from src.utils.logger import logger
from src.db.connection import execute_db_query
from mysql.connector import <PERSON><PERSON><PERSON>


def mark_conversation_as_good_case(conversation_id: str, is_good_case: bool = True, marked_by: str = None,
                                 feedback_tags: list = None, custom_feedback: str = None) -> bool:
    """
    Mark or unmark a conversation as a good case using the good_case table.

    Args:
        conversation_id (str): The ID of the conversation to mark
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        marked_by (str, optional): The username of the person marking the good case. Defaults to None.
        feedback_tags (list, optional): List of selected feedback tags. Defaults to None.
        custom_feedback (str, optional): Custom feedback text from user. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to mark a good case")
        return False

    try:
        if is_good_case:
            # Mark as good case - insert into good_case table
            # First, verify the conversation exists
            check_sql = "SELECT COUNT(*) as count FROM chat_history WHERE conversation_id = %s LIMIT 1"
            result = execute_db_query(check_sql, (conversation_id,), fetch='one')

            if not result or result['count'] == 0:
                logger.warning(f"No chat history found for conversation {conversation_id}")
                return False

            # 处理反馈数据
            feedback_tags_json = None
            feedback_submitted_at = None

            if feedback_tags or custom_feedback:
                # 如果有反馈数据，转换标签为JSON字符串并设置提交时间
                if feedback_tags:
                    try:
                        feedback_tags_json = json.dumps(feedback_tags, ensure_ascii=False)
                    except (TypeError, ValueError) as e:
                        logger.warning(f"Failed to serialize feedback tags {feedback_tags}: {e}")
                        feedback_tags_json = None

                feedback_submitted_at = datetime.now()

            # Insert into good_case table (or update if exists)
            insert_sql = """
                INSERT INTO good_case (conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at)
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    marked_by = VALUES(marked_by),
                    feedback_tags = VALUES(feedback_tags),
                    custom_feedback = VALUES(custom_feedback),
                    feedback_submitted_at = VALUES(feedback_submitted_at),
                    updated_at = CURRENT_TIMESTAMP
            """
            values = (conversation_id, marked_by, feedback_tags_json, custom_feedback, feedback_submitted_at)
            execute_db_query(insert_sql, values, commit=True)

            feedback_info = ""
            if feedback_tags or custom_feedback:
                feedback_info = f" with feedback (tags: {feedback_tags}, text: {bool(custom_feedback)})"
            logger.info(f"Successfully marked conversation {conversation_id} as good case by {marked_by}{feedback_info}")

        else:
            # Unmark as good case - remove from good_case table
            delete_sql = "DELETE FROM good_case WHERE conversation_id = %s"
            affected_rows = execute_db_query(delete_sql, (conversation_id,), fetch='count', commit=True)
            logger.info(f"Successfully unmarked conversation {conversation_id} as good case, {affected_rows} records removed")

        return True

    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error marking conversation as good case: {e}", exc_info=True)
        return False


def get_conversation_good_case_status(conversation_id: str) -> bool:
    """
    Check if a conversation is marked as a good case using the good_case table.

    Args:
        conversation_id (str): The ID of the conversation to check

    Returns:
        bool: True if the conversation is marked as a good case, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to check good case status")
        return False

    # Query the good_case table
    sql = "SELECT id FROM good_case WHERE conversation_id = %s"
    params = [conversation_id]

    # Limit to one row since we just need to check if the record exists
    sql += " LIMIT 1"

    try:
        result = execute_db_query(sql, params, fetch='one')
        is_good_case = result is not None
        logger.debug(f"Conversation {conversation_id} good case status: {is_good_case}")
        return is_good_case

    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking good case status: {e}", exc_info=True)
        return False


def get_conversation_good_case_feedback(conversation_id: str) -> dict:
    """
    Get feedback data for a good case conversation.

    Args:
        conversation_id (str): The ID of the conversation to get feedback for

    Returns:
        dict: Feedback data including tags, custom text, and submission time, or None if not marked as good case
              If marked as good case but no feedback submitted, returns a dict with empty feedback data
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to get good case feedback")
        return None

    # 修改查询：移除 feedback_submitted_at IS NOT NULL 条件，以便获取所有标记为good case的记录
    sql = """
        SELECT feedback_tags, custom_feedback, feedback_submitted_at
        FROM good_case
        WHERE conversation_id = %s
        LIMIT 1
    """

    try:
        result = execute_db_query(sql, (conversation_id,), fetch='one')

        if not result:
            # 没有标记为good case
            return None

        feedback_data = {
            'feedback_tags': [],
            'custom_feedback': result.get('custom_feedback', ''),
            'feedback_submitted_at': result.get('feedback_submitted_at')
        }

        # 解析JSON格式的标签数据
        feedback_tags_json = result.get('feedback_tags')
        if feedback_tags_json:
            try:
                feedback_data['feedback_tags'] = json.loads(feedback_tags_json)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse feedback tags JSON for conversation {conversation_id}: {e}")
                feedback_data['feedback_tags'] = []

        # 如果没有提交反馈，但已标记为good case，返回空的反馈数据结构
        # 这样前端就能知道已经标记为good case，但还没有填写反馈
        return feedback_data

    except Error as e:
        logger.error(f"Database error getting good case feedback for {conversation_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting good case feedback: {e}", exc_info=True)
        return None


def get_bulk_good_case_feedback(conversation_ids: list) -> dict:
    """
    批量获取多个对话的好案例反馈信息

    Args:
        conversation_ids (list): 对话ID列表

    Returns:
        dict: 以conversation_id为key，反馈数据为value的字典
    """
    if not conversation_ids:
        return {}

    # 移除重复的ID并过滤掉空值
    conversation_ids = list(set(filter(None, conversation_ids)))
    if not conversation_ids:
        return {}

    # 构建查询参数占位符
    placeholders = ','.join(['%s'] * len(conversation_ids))
    sql = f"""
        SELECT conversation_id, feedback_tags, custom_feedback, feedback_submitted_at
        FROM good_case
        WHERE conversation_id IN ({placeholders})
    """

    try:
        results = execute_db_query(sql, tuple(conversation_ids), fetch='all')
        
        feedback_map = {}
        for result in results:
            conversation_id = result.get('conversation_id')
            if not conversation_id:
                continue
                
            feedback_data = {
                'feedback_tags': [],
                'custom_feedback': result.get('custom_feedback', ''),
                'feedback_submitted_at': result.get('feedback_submitted_at')
            }

            # 解析JSON格式的标签数据
            feedback_tags_json = result.get('feedback_tags')
            if feedback_tags_json:
                try:
                    feedback_data['feedback_tags'] = json.loads(feedback_tags_json)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"Failed to parse feedback tags JSON for conversation {conversation_id}: {e}")
                    feedback_data['feedback_tags'] = []

            feedback_map[conversation_id] = feedback_data

        return feedback_map

    except Error as e:
        logger.error(f"Database error getting bulk good case feedback: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error getting bulk good case feedback: {e}", exc_info=True)
        return {}


