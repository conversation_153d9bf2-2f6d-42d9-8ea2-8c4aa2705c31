"""
用户仓储实现

负责用户数据的持久化操作，遵循DDD架构模式
"""

from abc import ABC, abstractmethod
from typing import Optional, List
from src.db.connection import execute_db_query, Database
from src.models.user_info_class import User
from src.utils.logger import logger


class UserRepository(ABC):
    """用户仓储接口"""
    
    @abstractmethod
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        pass
    
    @abstractmethod
    def find_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查找用户"""
        pass
    
    @abstractmethod
    def find_by_open_id(self, open_id: str) -> Optional[User]:
        """根据open_id查找用户"""
        pass
    
    @abstractmethod
    def search_users(self, search_term: str, limit: int = 5) -> List[User]:
        """模糊搜索用户"""
        pass
    
    @abstractmethod
    def list_users(self, limit: int = 10) -> List[User]:
        """获取用户列表"""
        pass


class ChatbiUserRepository(UserRepository):
    """ChatBI数据库用户仓储实现"""
    
    def _build_user_from_result(self, result: dict) -> User:
        """从数据库结果构建用户实体"""
        return User(
            name=result['name'],
            email=result['email'],
            open_id=result['open_id'],
            avatar_url=result.get('avatar'),
            user_id=result.get('user_id'),
            job_title=result.get('job_title'),
            department=result.get('first_level_department'),
            phone=None  # 数据库中暂无电话字段
        )
    
    def _get_base_query(self) -> str:
        """获取基础查询SQL"""
        return """
            SELECT 
                name, 
                email, 
                open_id,
                avatar,
                user_id,
                job_title,
                first_level_department
            FROM user
        """
    
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        try:
            sql = self._get_base_query() + " WHERE name = %s LIMIT 1"
            result = execute_db_query(sql, params=(username,), fetch='one', database=Database.CHATBI)
            
            if result:
                user = self._build_user_from_result(result)
                logger.info(f"✅ 找到用户: {user.name} ({user.email})")
                return user
            else:
                logger.warning(f"❌ 未找到用户: {username}")
                return None
                
        except Exception as e:
            logger.error(f"根据用户名查找用户失败 {username}: {e}")
            return None
    
    def find_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查找用户"""
        try:
            sql = self._get_base_query() + " WHERE email = %s LIMIT 1"
            result = execute_db_query(sql, params=(email,), fetch='one', database=Database.CHATBI)
            
            if result:
                user = self._build_user_from_result(result)
                logger.info(f"✅ 找到用户邮箱: {user.email} -> {user.name}")
                return user
            else:
                logger.warning(f"❌ 未找到邮箱: {email}")
                return None
                
        except Exception as e:
            logger.error(f"根据邮箱查找用户失败 {email}: {e}")
            return None
    
    def find_by_open_id(self, open_id: str) -> Optional[User]:
        """根据open_id查找用户"""
        try:
            sql = self._get_base_query() + " WHERE open_id = %s LIMIT 1"
            result = execute_db_query(sql, params=(open_id,), fetch='one', database=Database.CHATBI)
            
            if result:
                user = self._build_user_from_result(result)
                logger.info(f"✅ 找到用户open_id: {open_id} -> {user.name}")
                return user
            else:
                logger.warning(f"❌ 未找到open_id: {open_id}")
                return None
                
        except Exception as e:
            logger.error(f"根据open_id查找用户失败 {open_id}: {e}")
            return None
    
    def search_users(self, search_term: str, limit: int = 5) -> List[User]:
        """模糊搜索用户"""
        try:
            sql = self._get_base_query() + " WHERE name LIKE %s OR email LIKE %s LIMIT %s"
            like_pattern = f"%{search_term}%"
            results = execute_db_query(
                sql, 
                params=(like_pattern, like_pattern, limit), 
                fetch='all', 
                database=Database.CHATBI
            ) or []
            
            users = []
            for result in results:
                users.append(self._build_user_from_result(result))
            
            logger.info(f"🔍 搜索 '{search_term}' 找到 {len(users)} 个用户")
            return users
            
        except Exception as e:
            logger.error(f"搜索用户失败: {e}")
            return []
    
    def list_users(self, limit: int = 10) -> List[User]:
        """获取用户列表"""
        try:
            sql = self._get_base_query() + " LIMIT %s"
            results = execute_db_query(sql, params=(limit,), fetch='all', database=Database.CHATBI) or []
            
            users = []
            for result in results:
                users.append(self._build_user_from_result(result))
            
            logger.info(f"📋 获取用户列表，共 {len(users)} 个用户")
            return users
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
