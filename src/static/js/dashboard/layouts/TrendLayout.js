/**
 * TrendLayout Component
 *
 * Dashboard trend analysis section with line chart
 * Fetches real data from dashboard daily usage API
 */
import { ref, reactive, onMounted, watch } from 'vue';
import TrendCard from '../components/cards/TrendCard.js';
import GroupSection from '../components/GroupSection.js';
import DateRangeSelector from '../../common/components/DateRangeSelector.js';
import DepartmentFilter from '../components/DepartmentFilter.js';
import { fetchDailyUsage, fetchDailyUsers } from '../services/dashboardService.js';

export default {
    name: 'TrendLayout',
    components: {
        TrendCard,
        GroupSection,
        DateRangeSelector,
        DepartmentFilter
    },
    setup() {
        // Loading state
        const isLoading = ref(true);

        // Date range options
        const dayRanges = [
            { id: 7, label: '最近7天' },
            { id: 30, label: '最近30天' },
            { id: 90, label: '最近90天' }
        ];
        const selectedDayRange = ref(7);

        // Department filter state
        const selectedDepartment = ref('');

        // Trend chart data
        const trendData = reactive({
            labels: [],
            datasets: [
                {
                    label: '查询数',
                    data: []
                },
                {
                    label: 'Bad Case',
                    data: []
                }
            ]
        });

        // 用户数趋势数据
        const userTrendData = reactive({
            labels: [],
            datasets: [
                {
                    label: '用户数',
                    data: []
                }
            ]
        });

        // Line colors for the trend chart
        const lineColors = [
            {
                light: 'rgb(59, 130, 246)', // Blue
                dark: 'rgb(96, 165, 250)'
            },
            {
                light: 'rgb(239, 68, 68)', // Red
                dark: 'rgb(248, 113, 113)'
            }
        ];

        // 用户数趋势颜色配置
        const userLineColors = [
            {
                light: 'rgb(251, 146, 60)', // Orange
                dark: 'rgb(251, 191, 36)'
            }
        ];

        // Load daily usage data
        const loadDailyUsage = async () => {
            isLoading.value = true;
            try {
                const [usageData, usersData] = await Promise.all([
                    fetchDailyUsage(selectedDayRange.value, selectedDepartment.value),
                    fetchDailyUsers(selectedDayRange.value, selectedDepartment.value)
                ]);
                // 更新查询趋势
                trendData.labels = usageData.dates;
                trendData.datasets[0].data = usageData.query_counts;
                trendData.datasets[1].data = usageData.bad_case_convo_counts;
                // 更新用户趋势
                userTrendData.labels = usersData.dates;
                userTrendData.datasets[0].data = usersData.user_counts;
            } catch (error) {
                console.error('Error loading trend data:', error);
            } finally {
                isLoading.value = false;
            }
        };

        // Watch for day range changes
        watch(() => selectedDayRange.value, (newDays) => {
            // Ensure the value is a number
            if (typeof newDays === 'string') {
                selectedDayRange.value = parseInt(newDays);
            }
            loadDailyUsage();
        });

        // Handle department filter change
        const handleDepartmentChange = (department) => {
            selectedDepartment.value = department;
            loadDailyUsage();
        };

        // Load data when component is mounted
        onMounted(() => {
            loadDailyUsage();
        });

        return {
            isLoading,
            dayRanges,
            selectedDayRange,
            selectedDepartment,
            trendData,
            lineColors,
            userTrendData,
            userLineColors,
            handleDepartmentChange
        };
    },
    template: `
        <GroupSection group-id="trend">
            <template #title>
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                    <h2 class="text-l font-bold">趋势分析</h2>
                    <div class="flex flex-col sm:flex-row items-end sm:items-center gap-3">
                        <DateRangeSelector
                            v-model="selectedDayRange"
                            :ranges="dayRanges"
                        />
                        <DepartmentFilter
                            :initial-department="selectedDepartment"
                            @department-change="handleDepartmentChange"
                        />
                    </div>
                </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
                <TrendCard
                    title="查询与问题趋势"
                    :subtitle="'最近' + selectedDayRange + '天数据分析'"
                    :chart-data="trendData"
                    :loading="isLoading"
                    :line-colors="lineColors"
                    size="xl"
                />
                <TrendCard
                    title="每日用户数趋势"
                    :subtitle="'最近' + selectedDayRange + '天活跃用户统计'"
                    :chart-data="userTrendData"
                    :loading="isLoading"
                    :line-colors="userLineColors"
                    size="xl"
                />
            </div>
        </GroupSection>
    `
};