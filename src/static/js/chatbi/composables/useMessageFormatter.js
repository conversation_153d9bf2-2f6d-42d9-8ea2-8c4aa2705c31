/**
 * Message Formatter Composable
 *
 * Handles formatting and standardization of chat messages
 */

/**
 * 生成唯一消息ID
 * @param {string} prefix 前缀
 * @returns {string} 唯一ID
 */
const generateMessageId = (prefix = 'msg') => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

/**
 * 解析用户消息内容，支持resource_url字段的图片消息
 * @param {string} content 消息内容
 * @param {string} resourceUrl 资源URL字段
 * @returns {Object} 包含text和images的对象
 */
const parseUserMessageContent = (content, resourceUrl) => {
    // 处理resource_url字段中的图片
    let images = [];
    if (resourceUrl && typeof resourceUrl === 'string') {
        // 将逗号分隔的URL字符串转换为数组
        images = resourceUrl.split(',').map(url => url.trim()).filter(url => url.length > 0);
    }

    // 检查是否是旧的JSON格式（向后兼容）
    try {
        const parsed = JSON.parse(content);
        if (parsed && typeof parsed === 'object' && 'text' in parsed) {
            return {
                text: parsed.text || '',
                images: parsed.images || images // 优先使用JSON中的images，如果没有则使用resource_url
            };
        }
    } catch (e) {
        // 不是JSON格式，当作纯文本处理
    }

    // 纯文本消息，使用resource_url中的图片
    return {
        text: content,
        images: images
    };
};

/**
 * 格式化消息
 * @param {Object} msg 原始消息对象
 * @returns {Object} 格式化后的消息对象
 */
const formatMessage = (msg) => {
    // 如果日志是字符串类型，保持原样；如果是数组，保持原样；否则设为空数组
    let formattedLogs = msg.logs;
    if (!msg.logs) {
        formattedLogs = [];
    }

    // 解析用户消息内容（如果是用户消息且包含图片信息）
    let content = msg.content;
    let images = [];

    if (msg.role === 'user') {
        const parsed = parseUserMessageContent(msg.content, msg.resource_url);
        content = parsed.text;
        images = parsed.images;
    }

    // 使用updated_at作为完成时间
    let displayTime;
    if (msg.updated_at) {
        // 处理updated_at格式，只保留时间部分
        const updatedDate = new Date(msg.updated_at);
        displayTime = updatedDate.toLocaleTimeString();
    } else if (msg.timestamp) {
        // 如果没有updated_at，使用timestamp作为后备
        displayTime = new Date(parseInt(msg.timestamp)).toLocaleTimeString();
    } else {
        displayTime = new Date().toLocaleTimeString();
    }

    return {
        id: msg.id ? String(msg.id) : generateMessageId(), // 确保ID是字符串类型
        role: msg.role,
        content: content,
        images: images, // 添加图片数组
        logs: formattedLogs, // 保留日志数据，保持原始类型
        timestamp: displayTime, // 现在显示的是完成时间（updated_at）
        time_spend: msg.time_spend || null, // 添加AI响应耗时（仅assistant角色有意义）
        isBadCase: msg.isBadCase || false, // 添加 bad case 状态
        isGoodCase: msg.isGoodCase || false, // 添加 good case 状态
        // 添加反馈信息
        goodCaseFeedback: msg.goodCaseFeedback || null,
        badCaseFeedback: msg.badCaseFeedback || null
    };
};

/**
 * 创建用户消息对象
 * @param {Object} userMessage 用户消息数据
 * @returns {Object} 格式化的用户消息对象
 */
const createUserMessage = (userMessage) => {
    const messageId = generateMessageId('user');
    return {
        id: messageId,
        role: 'user',
        content: userMessage.content,
        images: userMessage.images || [], // 添加图片支持
        timestamp: new Date(userMessage.updated_at || userMessage.timestamp).toLocaleTimeString()
    };
};

/**
 * 创建AI消息对象（初始空消息，用于流式更新）
 * @returns {Object} 初始AI消息对象
 */
const createAiMessage = () => {
    const newMessageId = generateMessageId('ai');
    return {
        id: newMessageId,
        role: 'assistant',
        content: '',
        renderedContent: '',
        timestamp: new Date().toLocaleTimeString(),
        isStreaming: true,
        isError: false,
        isInterrupted: false // 添加中断状态标记
    };
};

/**
 * 创建历史记录用的AI消息对象
 * @param {Object} message 消息对象
 * @param {Object} messageData 消息数据
 * @returns {Object} 格式化的AI消息对象
 */
const createHistoryAiMessage = (message, messageData) => {
    // 使用updated_at作为完成时间，如果没有则使用timestamp
    const displayTime = messageData.updated_at ? 
        new Date(messageData.updated_at).toLocaleTimeString() : 
        new Date(messageData.timestamp).toLocaleTimeString();

    return {
        id: message.id,
        role: 'assistant',
        content: messageData.content,
        timestamp: displayTime,
        time_spend: messageData.time_spend || null,
        logs: messageData.logs || message.logs || [], // 保存日志数据
        // 添加反馈信息支持
        isGoodCase: message.isGoodCase || messageData.isGoodCase || false,
        isBadCase: message.isBadCase || messageData.isBadCase || false,
        goodCaseFeedback: message.goodCaseFeedback || messageData.goodCaseFeedback || null,
        badCaseFeedback: message.badCaseFeedback || messageData.badCaseFeedback || null
    };
};

/**
 * 使用消息格式化器
 * @returns {Object} 消息格式化方法
 */
export function useMessageFormatter() {
    return {
        formatMessage,
        createUserMessage,
        createAiMessage,
        createHistoryAiMessage,
        generateMessageId
    };
}
