/**
 * Query Service
 *
 * Provides functions for interacting with the query API endpoint.
 * Handles sending queries, processing streaming responses, and interrupting ongoing queries.
 */
import { renderMarkdown } from '../../utils/MarkdownRenderer.js';

// --- State ---
// 当前活动的会话ID
let currentConversationId = null;
// 所有会话历史记录，以ID为键
let allConversations = {};

// 移除 debounce 机制，使用直接渲染

/**
 * 发送查询并处理流式响应
 *
 * @param {Object} options - 查询选项
 * @param {string} options.query - 用户查询文本
 * @param {Array} options.images - 图片URL数组（可选）
 * @param {string|null} options.conversationId - 会话ID，如果是新会话则为null
 * @param {Array} options.history - 历史消息数组，用于提供上下文
 * @param {Function} options.onMessageStart - 消息开始回调，接收初始状态对象
 * @param {Function} options.onMessageUpdate - 消息更新回调，接收数据块和状态对象
 * @param {Function} options.onMessageComplete - 消息完成回调，接收最终状态对象
 * @param {Function} options.onError - 错误处理回调，接收错误信息和状态对象
 * @returns {Object} - 包含abort方法的控制器对象，用于中断请求
 */
export function sendQuery({
    query,
    images = [],
    conversationId = null,
    history = [],
    onMessageStart = () => {},
    onMessageUpdate = () => {},
    onMessageComplete = () => {},
    onError = () => {}
}) {
    // 创建 AbortController 用于中断请求
    const controller = new AbortController();
    const { signal } = controller;

    // 准备请求数据
    const requestData = {
        query: query,
        timestamp: Date.now()
    };

    // 如果有图片，添加到请求中
    if (images && images.length > 0) {
        requestData.images = images;
    }

    // 如果有会话ID，添加到请求中
    if (conversationId) {
        requestData.conversation_id = conversationId;
    }

    // 如果有历史记录，添加到请求中
    if (history && history.length > 0) {
        requestData.history = history;
    }

    // 添加用户信息（包括位置信息）
    if (window.userInfo) {
        requestData.user_info = {
            name: window.userInfo.name || '访客',
            location: window.userInfo.location || null
        };
    }

    // 更新当前会话ID
    if (conversationId) {
        currentConversationId = conversationId;
    }

    // 如果是新会话，初始化会话历史
    if (currentConversationId && !allConversations[currentConversationId]) {
        allConversations[currentConversationId] = [];
    }

    // 如果不是重试，将用户消息添加到历史记录
    if (currentConversationId && query) {
        const userMessage = { role: 'user', content: query, timestamp: Date.now() };
        if (!allConversations[currentConversationId]) {
            allConversations[currentConversationId] = [];
        }
        allConversations[currentConversationId].push(userMessage);
    }

    // 创建响应状态对象
    const responseState = {
        rawContent: '',       // 原始Markdown内容
        renderedContent: '',  // 渲染后的HTML内容
        logs: [],             // 日志消息数组
        logsHTML: '',         // 日志HTML
        isComplete: false,    // 是否完成
        conversationId: currentConversationId, // 会话ID
        isInterrupted: false, // 是否被中断
        userQuery: query      // 用户查询
    };

    // 通知开始处理消息
    onMessageStart(responseState);

    // 发送请求
    fetch('/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal // 传递 AbortSignal 以支持中断
    })
    .then(response => {
        // 检查响应状态
        if (!response.ok) {
            // 尝试解析错误响应
            return response.json().then(errorData => {
                throw new Error(errorData.error || `请求失败: ${response.status}`);
            }).catch(() => {
                // 如果不是JSON格式，使用状态文本
                throw new Error(`请求失败: ${response.status} ${response.statusText}`);
            });
        }

        // 获取会话ID（如果是新会话）
        const newConversationId = response.headers.get('X-Conversation-ID');
        if (newConversationId) {
            responseState.conversationId = newConversationId;
        }

        // 创建流式响应的读取器
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = ''; // 用于存储不完整的行

        // 处理单行数据
        function processLine(line) {
            if (!line.startsWith('[data]:')) {
                // 添加到日志
                const logEntry = `Unexpected stream line: ${line}`;
                responseState.logs.push(logEntry);
                responseState.logsHTML += `<p class="log-line warning">${logEntry.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>\n`;
                return;
            }

            // 提取JSON部分
            const jsonPart = line.substring('[data]:'.length).trim();
            if (!jsonPart) return;

            try {
                // 解析JSON数据
                const parsedData = JSON.parse(jsonPart);

                // 根据数据类型处理
                if (parsedData.type === 'data' && parsedData.content) {
                    // 更新原始内容
                    responseState.rawContent += parsedData.content;

                    // 直接渲染并通知更新，不再使用 debounce
                    responseState.renderedContent = renderMarkdown(responseState.rawContent);
                    onMessageUpdate(parsedData.content, responseState);
                }
                else if (parsedData.type === 'error') {
                    // 处理错误
                    const errorLog = `Error: ${parsedData.content}`;
                    responseState.logs.push(errorLog);
                    responseState.logsHTML += `<p class="log-line error">${errorLog.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>\n`;
                    onError(parsedData.content, responseState);
                }
                else if (parsedData.content) {
                    // 处理其他类型的消息（如日志）
                    const logEntry = `${parsedData.type || 'log'}: ${parsedData.content}`;
                    responseState.logs.push(logEntry);
                    responseState.logsHTML += `<p class="log-line">${logEntry.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>\n`;
                    // 可以选择是否通知更新
                    onMessageUpdate(null, responseState);
                }
            } catch (error) {
                // 处理解析错误
                const errorLog = `Error processing line: ${line}`;
                responseState.logs.push(errorLog);
                responseState.logsHTML += `<p class="log-line error">${errorLog.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>\n`;
            }
        }

        // 递归函数，用于读取流式数据
        function readStream() {
            return reader.read().then(({ done, value }) => {
                // 如果已完成，处理完整消息
                if (done) {
                    // 确保内容已渲染（虽然现在应该已经渲染了）
                    responseState.renderedContent = renderMarkdown(responseState.rawContent);
                    responseState.isComplete = true;
                    onMessageComplete(responseState);
                    return;
                }

                // 解码数据块
                const chunk = decoder.decode(value, { stream: true });
                buffer += chunk;

                // 处理完整行
                let newlineIndex;
                while ((newlineIndex = buffer.indexOf('\n')) >= 0) {
                    const line = buffer.substring(0, newlineIndex).trim();
                    buffer = buffer.substring(newlineIndex + 1);
                    if (line) {
                        processLine(line);
                    }
                }

                // 继续读取下一块数据
                return readStream();
            });
        }

        // 开始读取流
        return readStream();
    })
    .catch(error => {
        // 检查是否是用户主动中断
        if (error.name === 'AbortError') {
            responseState.isInterrupted = true;
            responseState.logs.push('查询已被用户中断');
            responseState.logsHTML += `<p class="log-line warning">查询已被用户中断</p>\n`;

            // 在原始内容末尾添加中断提示，但不覆盖已有内容
            if (responseState.rawContent) {
                // 如果已有内容，添加换行和中断提示
                responseState.rawContent += '\n\n*回复已被中断*';
                responseState.renderedContent = renderMarkdown(responseState.rawContent);
            } else {
                // 如果没有内容，设置一个简单的中断提示
                responseState.rawContent = '*回复已被中断*';
                responseState.renderedContent = renderMarkdown(responseState.rawContent);
            }

            // 标记为完成，这样UI可以正确处理中断状态
            responseState.isComplete = true;

            // 通知完成而不是错误，这样UI不会清除内容
            onMessageComplete(responseState);
        } else {
            responseState.logs.push(`错误: ${error.message || '查询处理过程中发生错误'}`);
            responseState.logsHTML += `<p class="log-line error">错误: ${(error.message || '查询处理过程中发生错误').replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>\n`;
            onError(error.message || '查询处理过程中发生错误', responseState);
        }
    });

    // 返回控制器对象，允许外部中断请求
    return {
        abort: () => {
            controller.abort();
        },
        // 获取当前会话ID
        getCurrentConversationId: () => currentConversationId,
        // 获取会话历史
        getConversationHistory: (id) => allConversations[id] || [],
        // 获取所有会话
        getAllConversations: () => allConversations
    };
}
