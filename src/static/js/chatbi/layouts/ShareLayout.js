/**
 * Share Layout Component
 *
 * Layout for viewing shared conversations
 * Styled with Apple/OpenAI-inspired aesthetics
 */
import { reactive, ref, onMounted, computed } from 'vue';
import ShareHeader from '../components/ShareHeader.js';
import ChatContent from '../components/ChatContent.js';
import DevLogPanel from '../components/DevLogPanel.js';
import ShareModal from '../components/modals/ShareModal.js';
import { zLayoutHeader } from '../../utils/zIndex.js';
import { useLayoutState } from '../composables/useLayoutState.js';
import { useLogState } from '../composables/useLogState.js';
import { useMessageFormatter } from '../composables/useMessageFormatter.js?v=1753410700';

export default {
    name: 'ShareLayout',
    components: {
        ShareHeader,
        ChatContent,
        DevLogPanel,
        ShareModal
    },
    setup() {
        // 获取用户信息
        const userInfo = reactive(window.userInfo || {
            name: '访客',
            avatar: '',
            isAdmin: false
        });

        // 获取共享会话信息
        const sharedConversation = reactive(window.sharedConversation || {
            isShared: false,
            isOwner: false,
            shareId: '',
            ownerName: '',
            messages: []
        });

        // 使用布局状态组合式API
        const layoutState = useLayoutState();

        // 使用日志状态组合式API
        const logState = useLogState();

        // 使用消息格式化器
        const messageFormatter = useMessageFormatter();

        // 开发日志面板状态
        const isDevLogVisible = ref(false);

        // 切换开发日志面板显示
        const toggleDevLog = () => {
            isDevLogVisible.value = !isDevLogVisible.value;
            // 保存状态到 localStorage，确保视图切换时保持状态
            localStorage.setItem('devLogVisible', isDevLogVisible.value ? 'true' : 'false');
        };

        // 从 localStorage 恢复日志面板状态
        const restoreDevLogState = () => {
            const savedState = localStorage.getItem('devLogVisible');
            if (savedState === 'true') {
                isDevLogVisible.value = true;
            }
        };

        // 分享链接查看功能
        const isShareLinkModalOpen = ref(false);

        // 构建当前页面的分享链接
        const currentShareUrl = computed(() => {
            return window.location.href;
        });

        // 显示分享链接模态框
        const viewShareLink = () => {
            isShareLinkModalOpen.value = true;
        };

        // 关闭分享链接模态框
        const closeShareLinkModal = () => {
            isShareLinkModalOpen.value = false;
        };

        // 格式化共享消息
        const messages = ref([]);

        // 在组件挂载时处理共享消息
        onMounted(() => {
            // 恢复日志面板状态
            restoreDevLogState();

            // 格式化共享消息
            if (sharedConversation.messages && Array.isArray(sharedConversation.messages)) {
                messages.value = sharedConversation.messages.map(messageFormatter.formatMessage);

                // 处理历史消息中的日志
                logState.processHistoryLogs(messages.value);
            }
        });

        return {
            // 用户信息
            userInfo,

            // 共享会话信息
            sharedConversation,

            // 布局状态
            isDarkTheme: layoutState.isDarkTheme,
            handleToggleTheme: layoutState.toggleTheme,

            // 消息状态
            messages,

            // 开发日志面板
            isDevLogVisible,
            devLogs: logState.currentLogs,
            toggleDevLog,

            // 分享链接查看功能
            isShareLinkModalOpen,
            currentShareUrl,
            viewShareLink,
            closeShareLinkModal,

            // Z-index 常量
            zLayoutHeader
        };
    },
    template: `
        <div class="h-screen flex flex-col min-w-0 overflow-hidden">
            <!-- Header - Fixed at top -->
            <div class="sticky top-0"
                 :style="{ zIndex: zLayoutHeader }">
                <ShareHeader
                    :user-info="userInfo"
                    :is-dark-theme="isDarkTheme"
                    :owner-name="sharedConversation.ownerName"
                    :is-dev-log-visible="isDevLogVisible"
                    @toggle-theme="handleToggleTheme"
                    @toggle-dev-log="toggleDevLog"
                    @view-share-link="viewShareLink"
                />
            </div>

            <!-- Main Content -->
            <div class="flex-1 overflow-hidden">
                <ChatContent
                    :messages="messages"
                    :is-loading-messages="false"
                    :message-error="null"
                    :active-conversation-id="sharedConversation.shareId"
                    :container-class="'w-full'"
                    :is-dev-log-visible="isDevLogVisible"
                    :dev-logs="devLogs"
                    :is-shared="true"
                    :is-owner="sharedConversation.isOwner"
                    @toggle-dev-log="toggleDevLog"
                    @share-conversation="viewShareLink"
                    @message-marked-as-bad-case="() => {}"
                />
            </div>

            <!-- 分享链接查看模态框 -->
            <ShareModal
                :is-open="isShareLinkModalOpen"
                :share-url="currentShareUrl"
                :is-generating="false"
                @close="closeShareLinkModal"
            />
        </div>
    `
};
