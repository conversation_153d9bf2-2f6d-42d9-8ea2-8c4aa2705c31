import {reactive, ref, onMounted, watch} from 'vue';
import ChatHeader from '../components/ChatbiHeader.js';
import HistorySidebar from '../components/HistorySidebar.js';
import ChatContent from '../components/ChatContent.js';
import DeleteConfirmModal from '../components/modals/DeleteConfirmModal.js';
import ShareModal from '../components/modals/ShareModal.js';
import DevLogPanel from '../components/DevLogPanel.js';
import {zLayoutHeader, zLayoutSidebar} from '../../utils/zIndex.js';
import {useLayoutState} from '../composables/useLayoutState.js';
import {useChatState} from '../composables/useChatState.js?v=1753410750';
import {useScrollManager} from '../composables/useScrollManager.js';
import {useMessageHandler} from '../composables/useMessageHandler.js?v=1753410800';
import ContentContainer from '../../common/layouts/ContentContainer.js';
import { useHistoryState } from '../composables/useHistoryState.js';

export default {
    name: 'AppLayout',
    components: {
        ChatHeader,
        HistorySidebar,
        ChatContent,
        DeleteConfirmModal,
        ShareModal,
        DevLogPanel,
        ContentContainer
    },
    setup() {
        // 获取用户信息
        const userInfo = reactive(window.userInfo || {
            name: '访客',
            avatar: '',
            isAdmin: false
        });

        // 使用布局状态组合式API
        const layoutState = useLayoutState();

        // 使用聊天状态组合式API
        const chatState = useChatState();

        // 使用滚动管理器组合式API
        const scrollManager = useScrollManager();

        // 获取用户位置信息
        const getUserLocation = async () => {
            try {
                // 检查浏览器是否支持地理位置API
                if (!navigator.geolocation) {
                    console.log('浏览器不支持地理位置功能');
                    return;
                }

                // 获取位置信息
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(
                        resolve,
                        reject,
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 300000 // 5分钟缓存
                        }
                    );
                });

                const { latitude, longitude, accuracy } = position.coords;

                // 将位置信息存储到用户信息中
                userInfo.location = {
                    latitude: latitude,
                    longitude: longitude,
                    accuracy: accuracy,
                    timestamp: Date.now()
                };

                // 同步到全局window.userInfo
                if (!window.userInfo) {
                    window.userInfo = {};
                }
                window.userInfo.location = userInfo.location;

                console.log('成功获取用户位置:', userInfo.location);

            } catch (error) {
                console.log('获取位置失败:', error.message);
                // 用户拒绝或其他错误，静默处理，不影响应用使用
            }
        };

        // 在组件挂载时恢复日志面板状态并从URL加载对话
        // Restore log panel state and load conversation from URL when component is mounted
        onMounted(() => {
            restoreDevLogState();
            // 从URL加载对话
            historyState.loadConversationFromUrl();
            // 获取用户位置
            getUserLocation();
        });
    
        // 使用消息处理器组合式API
        const messageHandler = useMessageHandler();
        const historyState = useHistoryState();
    
        // 监测 historyState 的 activeConversationId 变化
        // 当 historyState.activeConversationId 因 URL 加载而改变时，同步到 chatState
        watch(() => historyState.activeConversationId.value, (newHistoryId, oldHistoryId) => {
            console.log('=== ChatbiLayout watcher 被触发 ===', { newHistoryId, oldHistoryId });
            // 我们响应 historyState 中活动ID的变化。
            // chatState.activeConversationId 是一个计算属性，它反映了 historyState.activeConversationId。

            if (newHistoryId) {
                console.log('=== ChatbiLayout watcher 调用 chatState.selectConversation ===', newHistoryId);
                // 如果 historyState 中有新的活动ID，则告诉 chatState 选择它。
                chatState.selectConversation(newHistoryId);
            } else {
                // 如果 historyState 的活动ID变为null（例如，URL仅为 #/chat），
                // 则告诉 chatState 开始一个新的对话。
                if (chatState.activeConversationId.value !== null) { // 只有当 chatState 认为还有活动会话时才切换到新会话
                    chatState.newConversation();
                }
            }
        }, { immediate: true }); // 使用 immediate: true 以便在组件挂载时也运行，并处理初始URL状态。
    
        // 开发日志面板状态
        const isDevLogVisible = ref(false);
        // 使用 chatState 中的日志状态
        const devLogs = chatState.currentLogs;

        // 切换开发日志面板显示
        const toggleDevLog = () => {
            isDevLogVisible.value = !isDevLogVisible.value;
            // 保存状态到 localStorage，确保视图切换时保持状态
            localStorage.setItem('devLogVisible', isDevLogVisible.value ? 'true' : 'false');
        };

        // 从 localStorage 恢复日志面板状态
        const restoreDevLogState = () => {
            const savedState = localStorage.getItem('devLogVisible');
            if (savedState === 'true') {
                isDevLogVisible.value = true;
            }
        };

        // 消息流处理函数，包含日志处理和自动滚动
        const handleAiMessageStreamWithLogs = (data) => {
            // 先调用 chatState 的 updateStreamingMessage 方法处理日志
            chatState.updateStreamingMessage(data);

            // 然后检查是否需要自动滚动（与 useMessageHandler 中的逻辑一致）
            if (!scrollManager.userHasScrolledUp.value) {
                // 使用简单滚动，优化性能
                scrollManager.simpleScrollToBottom();
            }
        };

        // 用户消息处理函数，包含日志重置和自动滚动
        const handleUserMessageWithLogReset = (message) => {
            // 调用原始处理函数添加用户消息
            const messageId = chatState.addUserMessage(message);

            // 用户发送消息时，总是滚动到底部（与 useMessageHandler 中的逻辑一致）
            setTimeout(() => {
                // 使用智能滚动，处理内容可能继续渲染的情况
                scrollManager.smartScrollToBottom();
            }, 50);

            return messageId;
        };

        // 消息完成处理函数，包含日志处理和自动滚动
        const handleAiMessageCompleteWithLogs = (messageData) => {
            // 先调用 chatState 的 completeStreamingMessage 方法处理日志
            chatState.completeStreamingMessage(messageData);

            // 然后检查是否需要自动滚动（与 useMessageHandler 中的逻辑一致）
            if (!scrollManager.userHasScrolledUp.value) {
                // 使用智能滚动，处理内容可能继续渲染的情况
                // 这里特别重要，因为AI回复完成后可能会有代码高亮等操作
                setTimeout(() => {
                    scrollManager.smartScrollToBottom();
                }, 50);
            }
        };

        // 简化后的选择对话函数
        const handleNewConversation = () => {
            // 直接调用 chatState 的 newConversation 方法
            const result = chatState.newConversation();

            // 如果是移动端，则关闭侧边栏
            if (!layoutState.isDesktopView.value && layoutState.sidebarOpen.value) {
                layoutState.toggleSidebar();
            }
            return result;
        };

        // 简化后的选择对话函数
        const handleSelectConversation = (conversationId) => {
            // 直接调用 chatState 的 selectConversation 方法
            chatState.selectConversation(conversationId);

            // 如果是移动端，则关闭侧边栏
            if (!layoutState.isDesktopView.value && layoutState.sidebarOpen.value) {
                layoutState.toggleSidebar();
            }

            // 直接定位到底部，不使用动画和复杂的滚动逻辑
            setTimeout(() => {
                const container = scrollManager.getActiveScrollContainer();
                if (container) {
                    // 直接设置滚动位置到底部，不使用scrollTo方法以避免任何动画
                    container.scrollTop = container.scrollHeight;
                }
            }, 100);
        };

        return {
            // 用户信息
            userInfo,

            // 布局状态
            sidebarOpen: layoutState.sidebarOpen,
            toggleSidebar: layoutState.toggleSidebar,
            isDarkTheme: layoutState.isDarkTheme,
            handleToggleTheme: layoutState.toggleTheme,
            isDesktopView: layoutState.isDesktopView,
            windowWidth: layoutState.windowWidth,

            // 滚动相关
            userHasScrolledUp: scrollManager.userHasScrolledUp,

            // 聊天状态
            handleNewConversation,
            handleSelectConversation,  // 使用我们自己的包装函数
            handleDeleteConversation: chatState.historyState.openDeleteConfirmModal,
            handleShareConversation: chatState.shareConversation,
            activeConversationId: chatState.activeConversationId,
            messages: chatState.messages,
            isLoadingMessages: chatState.isLoadingMessages,
            messageError: chatState.messageError,

            // 消息处理方法 - 使用自定义处理函数确保滚动功能正常工作
            handleUserMessage: handleUserMessageWithLogReset, // 使用自定义处理函数清空日志并滚动
            handleAiMessageStart: (message) => {
                // 添加AI消息
                const messageId = chatState.addAiMessage(message);

                // 如果用户没有向上滚动，则滚动到底部
                if (!scrollManager.userHasScrolledUp.value) {
                    setTimeout(() => {
                        // 使用智能滚动，处理内容可能继续渲染的情况
                        scrollManager.smartScrollToBottom();
                    }, 50);
                }

                return messageId;
            },
            handleAiMessageStream: handleAiMessageStreamWithLogs, // 使用自定义处理函数捕获日志并滚动
            handleAiMessageComplete: handleAiMessageCompleteWithLogs, // 使用自定义处理函数保存日志并滚动
            handleMessageError: messageHandler.handleMessageError,

            // 开发日志面板
            isDevLogVisible,
            devLogs,
            toggleDevLog,

            // Z-index 常量
            zLayoutHeader,
            zLayoutSidebar,

            // 直接暴露 chatState 以便在模板中使用
            chatState
        };
    },
    template: `
        <div class="h-screen flex flex-col min-w-0 overflow-hidden">
            <!-- Header - Fixed at top -->
            <div class="sticky top-0" :style="{ zIndex: zLayoutHeader }">
                <ChatHeader
                    :user-info="userInfo"
                    :is-dark-theme="isDarkTheme"
                    :sidebar-open="sidebarOpen"
                    :is-desktop-view="isDesktopView"
                    :active-conversation-id="activeConversationId"
                    :is-dev-log-visible="isDevLogVisible"
                    @toggle-theme="handleToggleTheme"
                    @toggle-sidebar="toggleSidebar"
                    @new-conversation="handleNewConversation"
                    @share-conversation="handleShareConversation"
                    @delete-conversation="handleDeleteConversation"
                    @toggle-dev-log="toggleDevLog"
                />
            </div>

            <!-- ContentContainer - 只包含侧边栏和内容区域 -->
            <ContentContainer
                :is-open="sidebarOpen"
                @toggle="toggleSidebar"
            >
                <!-- Sidebar -->
                <template #sidebar>
                    <HistorySidebar
                        :is-open="sidebarOpen"
                        :user-info="userInfo"
                        @close-sidebar="toggleSidebar"
                        @new-conversation="handleNewConversation"
                        @toggle-sidebar="toggleSidebar"
                        @select-conversation="handleSelectConversation"
                        @delete-conversation="handleDeleteConversation"
                        @share-conversation="handleShareConversation"
                    />
                </template>

                <!-- Main Content -->
                <template #mainContent>
                    <ChatContent
                        :messages="messages"
                        :is-loading-messages="isLoadingMessages"
                        :message-error="messageError"
                        :active-conversation-id="activeConversationId"
                        :container-class="'w-full'"
                        :is-dev-log-visible="isDevLogVisible"
                        :dev-logs="devLogs"
                        @message-sent="handleUserMessage"
                        @message-start="handleAiMessageStart"
                        @message-stream="handleAiMessageStream"
                        @message-complete="handleAiMessageComplete"
                        @message-error="handleMessageError"
                        @message-interrupting="() => {}"
                        @toggle-dev-log="toggleDevLog"
                        @share-conversation="handleShareConversation"
                        @message-marked-as-bad-case="() => {}"
                    />
                </template>
            </ContentContainer>

            <!-- Modals -->
            <DeleteConfirmModal
                :is-open="chatState.historyState.deleteConfirmModal.isOpen"
                :title="chatState.historyState.deleteConfirmModal.title"
                @confirm="chatState.historyState.confirmDeleteConversation"
                @cancel="chatState.historyState.closeDeleteConfirmModal"
            />

            <ShareModal
                :is-open="chatState.isShareModalOpen.value"
                :share-url="chatState.shareUrl.value"
                :is-generating="chatState.isGeneratingShareLink.value"
                @close="chatState.closeShareModal"
            />
        </div>
    `
};
