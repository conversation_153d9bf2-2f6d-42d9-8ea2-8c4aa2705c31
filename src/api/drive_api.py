"""飞书云盘API模块
提供飞书云盘相关的API接口
"""

from flask import Blueprint, request, jsonify
from src.utils.logger import logger
from src.services.feishu.drive_service import DriveService
from src.services.auth.user_login_with_feishu import get_user_info_by_open_id
from src.utils.user_utils import get_valid_user_email

# 创建蓝图
drive_api = Blueprint('drive_api', __name__)


@drive_api.route('/api/drive/ensure-folder', methods=['POST'])
def ensure_chatbi_folder():
    """确保用户拥有ChatBI文件夹的API接口
    
    请求体应包含:
    {
        "open_id": "用户的飞书open_id"
    }
    
    返回:
    {
        "success": true/false,
        "data": {
            "folder_token": "文件夹token"
        },
        "message": "操作结果消息"
    }
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "请求体不能为空"
            }), 400
        
        open_id = data.get('open_id')
        if not open_id:
            return jsonify({
                "success": False,
                "message": "open_id参数是必需的"
            }), 400
        
        # 验证用户是否存在
        user_info = get_user_info_by_open_id(open_id)
        if not user_info:
            return jsonify({
                "success": False,
                "message": "用户不存在"
            }), 404
        
        # 确保用户拥有ChatBI文件夹
        folder_token = DriveService.ensure_user_chatbi_folder(open_id)
        
        if folder_token:
            logger.info(f"成功为用户确保ChatBI文件夹: open_id={open_id}, folder_token={folder_token}")
            return jsonify({
                "success": True,
                "data": {
                    "folder_token": folder_token
                },
                "message": "成功确保ChatBI文件夹"
            })
        else:
            logger.error(f"为用户确保ChatBI文件夹失败: open_id={open_id}")
            return jsonify({
                "success": False,
                "message": "确保ChatBI文件夹失败，请检查用户权限或网络连接"
            }), 500
            
    except Exception as e:
        logger.error(f"确保ChatBI文件夹API出错: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "message": "服务器内部错误"
        }), 500


@drive_api.route('/api/drive/folder-info', methods=['GET'])
def get_user_folder_info():
    """获取用户文件夹信息的API接口
    
    查询参数:
    - open_id: 用户的飞书open_id
    
    返回:
    {
        "success": true/false,
        "data": {
            "folder_token": "文件夹token",
            "folder_name": "文件夹名称"
        },
        "message": "操作结果消息"
    }
    """
    try:
        open_id = request.args.get('open_id')
        if not open_id:
            return jsonify({
                "success": False,
                "message": "open_id参数是必需的"
            }), 400
        
        # 验证用户是否存在
        user_info = get_user_info_by_open_id(open_id)
        if not user_info:
            return jsonify({
                "success": False,
                "message": "用户不存在"
            }), 404
        
        # 获取用户文件夹信息
        folder_info = DriveService.get_user_folder_info(open_id)
        
        if folder_info:
            return jsonify({
                "success": True,
                "data": folder_info,
                "message": "成功获取用户文件夹信息"
            })
        else:
            return jsonify({
                "success": False,
                "message": "用户暂无文件夹信息"
            }), 404
            
    except Exception as e:
        logger.error(f"获取用户文件夹信息API出错: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "message": "服务器内部错误"
        }), 500


@drive_api.route('/api/drive/folder-info', methods=['POST'])
def update_user_folder_info():
    """更新用户文件夹信息的API接口
    
    请求体应包含:
    {
        "open_id": "用户的飞书open_id",
        "folder_token": "文件夹token",
        "folder_name": "文件夹名称"
    }
    
    返回:
    {
        "success": true/false,
        "message": "操作结果消息"
    }
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "请求体不能为空"
            }), 400
        
        open_id = data.get('open_id')
        folder_token = data.get('folder_token')
        folder_name = data.get('folder_name')
        
        if not all([open_id, folder_token, folder_name]):
            return jsonify({
                "success": False,
                "message": "open_id、folder_token和folder_name参数都是必需的"
            }), 400
        
        # 验证用户是否存在
        user_info = get_user_info_by_open_id(open_id)
        if not user_info:
            return jsonify({
                "success": False,
                "message": "用户不存在"
            }), 404
        
        # 更新用户文件夹信息
        success = DriveService.update_user_folder_info(open_id, folder_token, folder_name)
        
        if success:
            logger.info(f"成功更新用户文件夹信息: open_id={open_id}, folder_token={folder_token}, folder_name={folder_name}")
            return jsonify({
                "success": True,
                "message": "成功更新用户文件夹信息"
            })
        else:
            logger.error(f"更新用户文件夹信息失败: open_id={open_id}")
            return jsonify({
                "success": False,
                "message": "更新用户文件夹信息失败"
            }), 500
            
    except Exception as e:
        logger.error(f"更新用户文件夹信息API出错: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "message": "服务器内部错误"
        }), 500