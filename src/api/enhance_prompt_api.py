"""
Prompt Enhancer API模块

提供智能提示词增强功能的API端点。
"""

from flask import Blueprint, request, jsonify, session
from src.services.auth.user_login_with_feishu import login_required
from src.utils.user_utils import get_valid_user_email
from src.services.prompt_enhancer_service import PromptEnhancerService
from src.utils.logger import logger

# 创建 Blueprint
enhance_prompt_bp = Blueprint('enhance_prompt', __name__, url_prefix='/api')


@enhance_prompt_bp.route('/enhance-prompt', methods=['POST'])
@login_required
def enhance_prompt():
    """
    POST /api/enhance-prompt 接口，智能增强用户的提示词。
    
    请求体:
        {
            "prompt": "用户的原始提示词"
        }
    
    返回:
        {
            "success": true,
            "enhanced_prompt": "增强后的提示词"
        }
        
    错误响应:
        {
            "success": false,
            "error": "错误信息"
        }
    """
    # 获取用户信息
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    job_title = user_info.get("job_title", "")

    if not username or not email:
        return jsonify({
            "success": False,
            "error": "用户信息不完整，请重新登录"
        }), 401

    # 获取请求数据
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "请求体不能为空"
            }), 400
            
        original_prompt = data.get("prompt", "").strip()
        if not original_prompt:
            return jsonify({
                "success": False,
                "error": "提示词不能为空"
            }), 400
            
        # 限制输入长度
        if len(original_prompt) > 2000:
            return jsonify({
                "success": False,
                "error": "提示词长度不能超过2000个字符"
            }), 400
            
    except Exception as e:
        logger.error(f"解析请求数据失败: {e}")
        return jsonify({
            "success": False,
            "error": "请求格式错误"
        }), 400

    logger.info(f"用户 {username} ({email}) 请求增强提示词: '{original_prompt[:100]}...'")

    try:
        # 创建提示词增强服务
        enhancer_service = PromptEnhancerService(
            user_email=email,
            user_name=username,
            user_job_title=job_title
        )
        
        # 执行提示词增强
        enhanced_prompt = enhancer_service.enhance_prompt(original_prompt)
        
        logger.info(f"用户 {email} 的提示词增强成功: '{original_prompt}' -> '{enhanced_prompt}'")
        
        return jsonify({
            "success": True,
            "enhanced_prompt": enhanced_prompt
        })
        
    except Exception as e:
        logger.error(f"用户 {email} 的提示词增强失败: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": f"提示词增强失败: {str(e)}"
        }), 500


@enhance_prompt_bp.route('/enhance-prompt/health', methods=['GET'])
@login_required
def enhance_prompt_health():
    """
    GET /api/enhance-prompt/health 接口，检查提示词增强服务的健康状态。
    
    返回:
        {
            "success": true,
            "status": "healthy",
            "message": "服务正常"
        }
    """
    try:
        # 简单的健康检查
        enhancer_service = PromptEnhancerService(
            user_email="<EMAIL>",
            user_name="Health Check",
            user_job_title="System"
        )
        
        # 测试系统能力加载
        capabilities = enhancer_service._load_chatbi_capabilities()
        
        if capabilities and len(capabilities.get("agents", {})) > 0:
            return jsonify({
                "success": True,
                "status": "healthy",
                "message": "服务正常",
                "agents_count": len(capabilities.get("agents", {}))
            })
        else:
            return jsonify({
                "success": False,
                "status": "unhealthy",
                "message": "系统能力加载失败"
            }), 500
            
    except Exception as e:
        logger.error(f"提示词增强服务健康检查失败: {e}")
        return jsonify({
            "success": False,
            "status": "unhealthy",
            "message": f"服务异常: {str(e)}"
        }), 500
