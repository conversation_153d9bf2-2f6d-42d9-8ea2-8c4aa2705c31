"""
推荐问题API接口模块。
提供获取AI推荐问题的HTTP接口。优先从离线表中获取推荐，如离线数据不存在则实时生成。
"""
import asyncio
from flask import request, jsonify, Blueprint, session

from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.services.auth.user_login_with_feishu import login_required
from src.services.chatbot.history_service import (
    get_user_latest_queries,
    get_other_users_latest_queries,
)
from src.services.recommendation.offline_recommendation_service import OfflineRecommendationService
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.in_memory_cache import in_memory_cache, clear_cache

# 创建推荐API的Blueprint
recommendation_bp = Blueprint('recommendation', __name__, url_prefix='/api')


@in_memory_cache(expire_seconds=12*3600)  # 缓存12小时，即12*3600
def get_cached_recommendations(user_email: str, count: int = 6) -> dict:
    """
    获取推荐的推荐问题列表。
    优先从离线表中获取，如果离线数据不存在则实时生成。

    Args:
        user_email (str): 用户邮箱，作为缓存key
        count (int): 推荐问题数量，默认6个，最大10个

    Returns:
        dict: 包含推荐结果的字典
        {
            "success": bool,
            "recommendations": List[str],
            "count": int,
            "source": str,  # "offline" 或 "realtime"
            "message": str (可选)
        }
    """
    logger.info(f"为用户 {user_email} 获取推荐问题（优先离线数据）,count:{count}")

    try:
        # 1. 尝试从离线表获取推荐
        offline_service = OfflineRecommendationService()
        offline_recommendations = offline_service.get_offline_recommendations(user_email, count)

        if offline_recommendations:
            logger.info(f"为用户 {user_email} 从离线表获取到 {len(offline_recommendations)} 个推荐问题")
            return {
                "success": True,
                "recommendations": offline_recommendations,
                "count": len(offline_recommendations),
                "source": "offline"
            }

        # 2. 如果离线推荐不存在，则实时生成
        logger.info(f"用户 {user_email} 离线推荐不存在，开始实时生成")
        
        # 获取当前用户的历史消息
        current_user_messages = get_user_latest_queries(user_email=user_email, limit=10)
        logger.info(f"为用户 {user_email} 获取到 {len(current_user_messages)} 条历史消息")

        # 获取其他用户的历史消息
        other_users_messages = get_other_users_latest_queries(current_user_email=user_email, limit=20)
        logger.info(f"为用户 {user_email} 获取到 {len(other_users_messages)} 条其他用户消息作为参考")

        # 检查是否有足够的历史数据
        if not current_user_messages and not other_users_messages:
            logger.info(f"用户 {user_email} 没有任何历史消息，返回空推荐列表")
            return {
                "success": True,
                "recommendations": [],
                "count": 0,
                "source": "realtime",
                "message": "暂无历史数据，无法生成推荐问题"
            }

        # 3. **这里实际上应该直接返回空列表，而不是实时生成**
        # **修改为**：实时生成功能将只由新的专门API提供，主API保持离线优先
        logger.info(f"用户 {user_email} 无离线数据，且历史数据不足，无法生成推荐")
        return {
            "success": True,
            "recommendations": [],
            "count": 0,
            "source": "no_data",
            "message": "暂无历史数据，无法生成推荐问题"
        }

    except Exception as e:
        logger.error(f"为用户 {user_email} 获取推荐问题时发生错误: {e}", exc_info=True)
        return {
            "success": False,
            "error": "获取推荐问题时发生内部错误，请稍后重试"
        }


@recommendation_bp.route('/recommendations', methods=['GET'])
@login_required
def get_recommendations():
    """
    GET /api/recommendations 接口，获取AI推荐的问题列表。
    优先从离线表中获取推荐，提高响应速度。

    查询参数:
        count (int, optional): 推荐问题数量，默认6个，最大10个

    返回:
        JSON格式的推荐问题列表
        {
            "success": true,
            "recommendations": ["问题1", "问题2", ...],
            "count": 实际返回的问题数量,
            "source": "offline" | "realtime"  (数据来源标识)
        }
    """
    # 获取用户信息
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)

    # 获取推荐数量参数
    try:
        count = int(request.args.get('count', 6))
        # 限制推荐数量范围
        if count < 1:
            count = 1
        elif count > 10:
            count = 10
    except ValueError:
        return jsonify({
            "success": False,
            "error": "count参数必须是有效的整数"
        }), 400

    logger.info(f"用户 {username} ({email}) 请求 {count} 个推荐问题")

    try:
        # 使用缓存函数获取推荐结果
        result = get_cached_recommendations(user_email=email, count=count)

        # 如果缓存函数返回错误，直接返回错误响应
        if not result.get("success", False):
            return jsonify(result), 500

        # 返回成功结果
        logger.info(f"为用户 {email} 返回了 {result.get('count', 0)} 个推荐问题（使用缓存）")
        return jsonify(result)

    except Exception as e:
        logger.error(f"为用户 {email} 获取推荐问题时发生错误: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "获取推荐问题时发生内部错误，请稍后重试"
        }), 500


@recommendation_bp.route('/recommendations/clear-cache', methods=['POST'])
@login_required
def clear_recommendations_cache():
    """
    POST /api/recommendations/clear-cache 接口，清除推荐问题的缓存。

    查询参数:
        user_email (str, optional): 指定要清除缓存的用户邮箱，如果不提供则清除所有推荐缓存

    返回:
        JSON格式的操作结果
        {
            "success": true,
            "message": "缓存清除成功"
        }
    """
    # 获取用户信息
    user_info = session.get("user_info")
    username = user_info.get("name")
    current_email = get_valid_user_email(user_info)

    # 获取要清除缓存的用户邮箱参数
    target_email = request.json.get('user_email') if request.json else None

    logger.info(f"用户 {username} ({current_email}) 请求清除推荐缓存，目标用户: {target_email or '所有用户'}")

    try:
        if target_email:
            # 清除指定用户的缓存
            # 需要构造缓存key来清除特定用户的缓存
            func_qualname = get_cached_recommendations.__qualname__
            clear_cache(func_qualname)  # 暂时清除整个函数的缓存，因为构造精确的key比较复杂
            message = f"已清除用户 {target_email} 的推荐缓存"
        else:
            # 清除所有推荐缓存
            func_qualname = get_cached_recommendations.__qualname__
            clear_cache(func_qualname)
            message = "已清除所有推荐缓存"

        logger.info(f"缓存清除成功: {message}")
        return jsonify({
            "success": True,
            "message": message
        })

    except Exception as e:
        logger.error(f"清除推荐缓存时发生错误: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "清除缓存时发生内部错误，请稍后重试"
        }), 500
