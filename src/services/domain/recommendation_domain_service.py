"""
推荐领域服务

处理推荐相关的核心业务逻辑，遵循DDD架构模式
"""

import asyncio
from typing import List, Optional
from datetime import datetime

from src.models.recommendation import (
    UserRecommendation, 
    RecommendationStatus, 
    ActiveUser,
    RecommendationGenerationTask,
    RecommendationQuery
)
from src.repositories.chatbi.recommendation import RecommendationRepository
from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.utils.logger import logger


class RecommendationDomainService:
    """推荐领域服务"""
    
    def __init__(self, recommendation_repository: RecommendationRepository):
        """
        初始化推荐领域服务
        
        Args:
            recommendation_repository: 推荐仓储接口实现
        """
        self.recommendation_repository = recommendation_repository
        self.default_recommendations_count = 10
        self.default_word_limit = 50
        self.default_expire_hours = 24
    
    def get_user_recommendations(self, user_email: str, count: int = 6) -> List[str]:
        """
        获取用户的有效推荐
        
        Args:
            user_email: 用户邮箱
            count: 需要返回的推荐数量
            
        Returns:
            List[str]: 推荐问题列表
        """
        if not user_email or not user_email.strip():
            logger.warning("用户邮箱不能为空")
            return []
        
        if count <= 0:
            logger.warning("推荐数量必须大于0")
            return []
        
        try:
            recommendation = self.recommendation_repository.find_by_user_email(user_email.strip())
            
            if recommendation and recommendation.is_valid():
                result = recommendation.get_recommendations_subset(count)
                logger.info(f"为用户 {user_email} 返回 {len(result)} 条有效推荐")
                return result
            else:
                logger.info(f"用户 {user_email} 没有有效的推荐")
                return []
                
        except Exception as e:
            logger.error(f"获取用户推荐失败: {user_email}, 错误: {e}", exc_info=True)
            return []
    
    def generate_recommendations_for_user(self, user: ActiveUser) -> bool:
        """
        为单个用户生成推荐
        
        Args:
            user: 活跃用户对象
            
        Returns:
            bool: 是否生成成功
        """
        try:
            # 1. 标记为生成中状态
            self.recommendation_repository.update_status(
                user.email, 
                user.open_id, 
                RecommendationStatus.GENERATING
            )
            
            # 2. 构建生成任务
            task = self._build_generation_task(user)
            
            if not task.has_sufficient_data():
                # 没有足够数据时使用默认推荐
                logger.warning(f"用户 {user.email} 没有足够的历史数据，使用默认推荐")
                default_recommendations = self._get_default_recommendations()
                self._save_completed_recommendation(user, default_recommendations)
                return True
            
            # 3. 调用AI生成推荐
            recommendations = self._generate_ai_recommendations(task)
            
            if not recommendations:
                # AI生成失败时使用默认推荐
                logger.warning(f"为用户 {user.email} AI生成推荐失败，使用默认推荐")
                recommendations = self._get_default_recommendations()
            
            # 4. 保存生成结果
            self._save_completed_recommendation(user, recommendations)
            
            # 5. 更新状态为完成
            self.recommendation_repository.update_status(
                user.email, 
                user.open_id, 
                RecommendationStatus.COMPLETED
            )
            
            logger.info(f"为用户 {user.email} 成功生成 {len(recommendations)} 条推荐")
            return True
            
        except Exception as e:
            # 标记为失败状态
            self.recommendation_repository.update_status(
                user.email, 
                user.open_id, 
                RecommendationStatus.FAILED,
                error_message=str(e)
            )
            logger.error(f"为用户 {user.email} 生成推荐失败: {e}", exc_info=True)
            return False
    
    def generate_recommendations_for_all_active_users(self, days: int = 30) -> dict:
        """
        为所有活跃用户生成推荐
        
        Args:
            days: 活跃用户的天数范围
            
        Returns:
            dict: 生成结果统计
        """
        logger.info(f"开始为最近 {days} 天的活跃用户生成推荐")
        
        try:
            # 获取活跃用户
            active_users = self.recommendation_repository.find_active_users(days)
            
            if not active_users:
                logger.info("没有找到活跃用户")
                return {"total": 0, "success": 0, "failed": 0}
            
            logger.info(f"找到 {len(active_users)} 个活跃用户")
            
            # 为每个用户生成推荐
            success_count = 0
            failed_count = 0
            
            for user in active_users:
                try:
                    if self.generate_recommendations_for_user(user):
                        success_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    failed_count += 1
                    logger.error(f"为用户 {user.email} 生成推荐时出错: {e}")
            
            result = {
                "total": len(active_users),
                "success": success_count,
                "failed": failed_count
            }
            
            logger.info(f"推荐生成完成: 总计 {result['total']} 个用户, 成功 {result['success']} 个, 失败 {result['failed']} 个")
            return result
            
        except Exception as e:
            logger.error(f"批量生成推荐失败: {e}", exc_info=True)
            return {"total": 0, "success": 0, "failed": 0, "error": str(e)}
    
    def _build_generation_task(self, user: ActiveUser) -> RecommendationGenerationTask:
        """构建推荐生成任务"""
        # 获取用户历史查询
        current_user_queries = self.recommendation_repository.find_user_queries(
            user.email, 
            limit=15
        )
        
        # 获取其他用户查询
        other_users_queries = self.recommendation_repository.find_other_users_queries(
            user.email, 
            limit=25
        )
        
        return RecommendationGenerationTask(
            user=user,
            current_user_queries=current_user_queries,
            other_users_queries=other_users_queries,
            recommendations_count=self.default_recommendations_count,
            word_limit=self.default_word_limit
        )
    
    def _generate_ai_recommendations(self, task: RecommendationGenerationTask) -> List[str]:
        """使用AI生成推荐"""
        try:
            # 创建推荐机器人
            bot = UserQueryRecommendationBot(
                task.user.get_user_info_dict(),
                count=task.recommendations_count,
                word_limit=task.word_limit
            )
            
            # 运行异步方法（同步执行）
            loop = None
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                recommendations = loop.run_until_complete(
                    bot.get_recommendations(
                        current_user_messages=task.get_current_user_contents(),
                        other_users_messages=task.get_other_users_contents()
                    )
                )
                
                return recommendations if recommendations else []
                
            finally:
                if loop:
                    loop.close()
                    
        except Exception as e:
            logger.error(f"AI生成推荐失败: {e}", exc_info=True)
            return []
    
    def _save_completed_recommendation(self, user: ActiveUser, recommendations: List[str]):
        """保存完成的推荐"""
        recommendation = UserRecommendation.create_new_recommendation(
            user_email=user.email,
            user_open_id=user.open_id,
            recommendations=recommendations,
            expire_hours=self.default_expire_hours
        )
        
        self.recommendation_repository.save(recommendation)
    
    def _get_default_recommendations(self) -> List[str]:
        """获取默认推荐问题"""
        return [
            "近7天华东仓热销商品库存情况如何？",
            "我的销售团队中谁的本月新客户开发最多？",
            "最近一个月安佳全脂牛奶的销售趋势是什么？",
            "杭州门店的订单履约率最近有什么变化？",
            "仓储物流团队上周的发货效率如何？",
            "哪些商品在华东地区表现最好？",
            "近两周新增门店的合作意向分析",
            "我的客户在哪些品类上复购率最高？",
            "最近30天的客户流失情况如何？",
            "如何评估销售团队的推广效果？"
        ]
    
    def validate_user_email(self, user_email: str) -> bool:
        """验证用户邮箱格式"""
        if not user_email or not user_email.strip():
            return False
        
        email = user_email.strip()
        return '@' in email and '.' in email.split('@')[1]
    
    def get_recommendation_statistics(self) -> dict:
        """获取推荐统计信息（可扩展）"""
        # 这里可以添加统计逻辑，比如成功率、用户覆盖率等
        # 目前返回基本信息
        return {
            "service_status": "active",
            "default_recommendations_count": self.default_recommendations_count,
            "default_expire_hours": self.default_expire_hours,
            "timestamp": datetime.now().isoformat()
        }
