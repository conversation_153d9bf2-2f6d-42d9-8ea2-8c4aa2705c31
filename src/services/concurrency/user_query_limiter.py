"""
用户查询限制器模块

该模块提供用户级别的查询并发控制，防止单个用户的慢查询影响其他用户。
"""

import asyncio
import time
from typing import Dict, Set
from dataclasses import dataclass

from models.query_result import SQLQueryResult
from src.utils.logger import logger
from src.config.concurrency_config import ConcurrencyConfig


@dataclass
class UserQueryStats:
    """用户查询统计信息"""
    active_queries: int = 0
    total_queries: int = 0
    last_query_time: float = 0
    slow_queries: int = 0  # 慢查询计数


class UserQueryLimiter:
    """用户查询限制器
    
    为每个用户维护独立的查询限制，防止某个用户的慢查询影响其他用户。
    """
    
    def __init__(self,
                 max_concurrent_per_user: int = None,
                 max_query_time_seconds: int = None,
                 slow_query_threshold_seconds: int = None):
        """
        初始化用户查询限制器

        Args:
            max_concurrent_per_user: 每个用户最大并发查询数（None时使用配置）
            max_query_time_seconds: 单个查询最大执行时间（秒）（None时使用配置）
            slow_query_threshold_seconds: 慢查询阈值（秒）（None时使用配置）
        """
        # 使用配置文件中的默认值
        self.max_concurrent_per_user = max_concurrent_per_user or ConcurrencyConfig.MAX_CONCURRENT_QUERIES_PER_USER
        self.max_query_time_seconds = max_query_time_seconds or ConcurrencyConfig.MAX_QUERY_TIME_SECONDS
        self.slow_query_threshold_seconds = slow_query_threshold_seconds or ConcurrencyConfig.SLOW_QUERY_THRESHOLD_SECONDS
        
        # 用户查询统计
        self._user_stats: Dict[str, UserQueryStats] = {}
        # 用户查询锁
        self._user_locks: Dict[str, asyncio.Semaphore] = {}
        # 活跃查询追踪
        self._active_queries: Dict[str, Set[str]] = {}
        # 全局锁保护数据结构
        self._global_lock = asyncio.Lock()
        
    async def _get_user_semaphore(self, user_id: str) -> asyncio.Semaphore:
        """获取用户的信号量"""
        async with self._global_lock:
            if user_id not in self._user_locks:
                self._user_locks[user_id] = asyncio.Semaphore(self.max_concurrent_per_user)
            return self._user_locks[user_id]
    
    async def _get_user_stats(self, user_id: str) -> UserQueryStats:
        """获取用户统计信息"""
        async with self._global_lock:
            if user_id not in self._user_stats:
                self._user_stats[user_id] = UserQueryStats()
            return self._user_stats[user_id]
    
    async def acquire_query_slot(self, user_id: str, query_id: str) -> bool:
        """
        为用户获取查询槽位

        Args:
            user_id: 用户ID
            query_id: 查询ID（用于追踪）

        Returns:
            bool: 是否成功获取槽位
        """
        semaphore = await self._get_user_semaphore(user_id)

        # 检查信号量是否已满，locked()在计数为0时返回True
        # 这是一个更安全的、使用公共API的方式来检查，而不是依赖内部变量_value
        if semaphore.locked():
            logger.warning(f"用户 {user_id} 已达到最大并发查询数 {self.max_concurrent_per_user}")
            return False

        # 非阻塞地获取信号量，理论上这里不会阻塞，因为前面已经检查过
        await semaphore.acquire()

        try:
            # 更新统计信息
            stats = await self._get_user_stats(user_id)
            stats.active_queries += 1
            stats.total_queries += 1
            stats.last_query_time = time.time()

            # 追踪活跃查询
            async with self._global_lock:
                if user_id not in self._active_queries:
                    self._active_queries[user_id] = set()
                self._active_queries[user_id].add(query_id)

            logger.info(f"用户 {user_id} 获取查询槽位成功，当前活跃查询: {stats.active_queries}")
            return True

        except Exception as e:
            # 关键修复：如果在更新统计信息时出错，必须释放已获取的信号量，防止资源泄漏
            logger.error(f"获取查询槽位后更新统计信息时出错: {e}", exc_info=True)
            semaphore.release()
            return False
    
    async def release_query_slot(self, user_id: str, query_id: str, execution_time: float = None):
        """
        释放用户查询槽位
        
        Args:
            user_id: 用户ID
            query_id: 查询ID
            execution_time: 查询执行时间（秒）
        """
        try:
            # 获取用户信号量并释放
            semaphore = await self._get_user_semaphore(user_id)
            semaphore.release()
            
            # 更新统计信息
            stats = await self._get_user_stats(user_id)
            stats.active_queries = max(0, stats.active_queries - 1)
            
            # 检查是否为慢查询
            if execution_time and execution_time > self.slow_query_threshold_seconds:
                stats.slow_queries += 1
                logger.warning(f"用户 {user_id} 执行慢查询，耗时: {execution_time:.2f}秒")
            
            # 移除活跃查询追踪
            async with self._global_lock:
                if user_id in self._active_queries:
                    self._active_queries[user_id].discard(query_id)
                    if not self._active_queries[user_id]:
                        del self._active_queries[user_id]
            
            logger.info(f"用户 {user_id} 释放查询槽位，当前活跃查询: {stats.active_queries}")
            
        except Exception as e:
            logger.error(f"释放查询槽位时出错: {e}", exc_info=True)
    
    async def execute_with_limit(self, user_id: str, query_id: str, query_func, *args, **kwargs):
        """
        在限制下执行查询
        
        Args:
            user_id: 用户ID
            query_id: 查询ID
            query_func: 要执行的查询函数
            *args, **kwargs: 传递给查询函数的参数
            
        Returns:
            SQLQueryResult: 查询结果
        """
        # 尝试获取槽位
        if not await self.acquire_query_slot(user_id, query_id):
            return SQLQueryResult.failed_result("查询被限制")
        
        start_time = time.time()
        try:
            # 执行查询（带超时）
            result = await asyncio.wait_for(
                query_func(*args, **kwargs),
                timeout=self.max_query_time_seconds
            )
            return result
            
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            return SQLQueryResult.failed_result(f"SQL查询超时，已执行时间: {execution_time:.2f}秒") 
            
        except Exception as e:
            return SQLQueryResult.failed_result(f"SQL查询执行出错: {str(e)}")
            
        finally:
            execution_time = time.time() - start_time
            await self.release_query_slot(user_id, query_id, execution_time)
    
    async def get_user_stats_summary(self, user_id: str) -> Dict:
        """获取用户统计摘要"""
        stats = await self._get_user_stats(user_id)
        return {
            "user_id": user_id,
            "active_queries": stats.active_queries,
            "total_queries": stats.total_queries,
            "slow_queries": stats.slow_queries,
            "last_query_time": stats.last_query_time,
            "slow_query_rate": stats.slow_queries / max(1, stats.total_queries)
        }
    
    async def get_global_stats(self) -> Dict:
        """获取全局统计信息"""
        async with self._global_lock:
            total_active = sum(len(queries) for queries in self._active_queries.values())
            total_users = len(self._user_stats)
            
            return {
                "total_active_queries": total_active,
                "total_users": total_users,
                "active_users": len(self._active_queries),
                "max_concurrent_per_user": self.max_concurrent_per_user,
                "max_query_time_seconds": self.max_query_time_seconds
            }


# 全局用户查询限制器实例
user_query_limiter = UserQueryLimiter()
