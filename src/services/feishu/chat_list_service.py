"""
飞书群聊列表服务
获取机器人所在的群组列表
"""

import json
from typing import List, Dict, Any, Optional
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from src.utils.logger import logger
from src.utils.in_memory_cache import in_memory_cache
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET


class ChatListService:
    """飞书群聊列表服务类"""
    
    def __init__(self):
        # 创建lark客户端
        self.client = (
            lark.Client.builder()
            .app_id(APP_ID)
            .app_secret(APP_SECRET)
            .timeout(10)
            .log_level(lark.LogLevel.INFO)
            .build()
        )
    
    @in_memory_cache(expire_seconds=300)  # 缓存5分钟
    def get_bot_chat_list(self, page_size: int = 50) -> List[Dict[str, Any]]:
        """获取机器人所在的群组列表
        
        Args:
            page_size: 每页返回的群组数量，默认50，最大100
            
        Returns:
            List[Dict[str, Any]]: 群组列表，每个群组包含chat_id、name、description等信息
        """
        try:
            logger.info(f"开始获取机器人所在的群组列表，page_size: {page_size}")
            
            all_chats = []
            page_token = None
            
            while True:
                # 构造请求对象
                request_builder = ListChatRequest.builder() \
                    .sort_type("ByActiveTimeDesc") \
                    .page_size(min(page_size, 100))  # 限制最大值为100
                
                if page_token:
                    request_builder.page_token(page_token)
                
                request = request_builder.build()
                
                # 发起请求
                response: ListChatResponse = self.client.im.v1.chat.list(request)
                
                # 处理失败返回
                if not response.success():
                    error_msg = f"获取群组列表失败, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                    logger.error(error_msg)
                    if response.raw and response.raw.content:
                        try:
                            error_detail = json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)
                            logger.error(f"详细错误信息: {error_detail}")
                        except:
                            logger.error(f"原始错误响应: {response.raw.content}")
                    return []
                
                # 处理业务结果
                if response.data and response.data.items:
                    for chat_item in response.data.items:
                        chat_info = {
                            'chat_id': chat_item.chat_id,
                            'name': chat_item.name or '',
                            'description': chat_item.description or '',
                            'avatar': getattr(chat_item, 'avatar', ''),
                            'owner_id': getattr(chat_item, 'owner_id', ''),
                            'member_count': getattr(chat_item, 'member_count', 0)
                        }
                        all_chats.append(chat_info)
                        logger.debug(f"群组: {chat_info['name']} ({chat_info['chat_id']})")
                
                # 检查是否还有更多数据
                if not response.data.has_more:
                    break
                    
                page_token = response.data.page_token
                
                # 安全检查，避免无限循环
                if len(all_chats) >= 1000:  # 限制最大返回1000个群组
                    logger.warning("群组数量超过1000个，停止获取")
                    break
            
            logger.info(f"成功获取到 {len(all_chats)} 个群组")
            return all_chats
            
        except Exception as e:
            logger.exception(f"获取群组列表时发生异常: {str(e)}")
            return []
    
    def get_group_chat_ids(self) -> List[str]:
        """获取所有群聊的chat_id列表
        
        Returns:
            List[str]: 群聊ID列表
        """
        try:
            chat_list = self.get_bot_chat_list()
            if chat_list:
                return [chat['chat_id'] for chat in chat_list]
        except Exception as e:
            logger.exception(f"获取群聊ID列表时发生异常: {str(e)}")
        return []
    
    def get_chat_info_by_id(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """根据chat_id获取群组信息
        
        Args:
            chat_id: 群组ID
            
        Returns:
            Optional[Dict[str, Any]]: 群组信息，如果未找到返回None
        """
        try:
            chat_list = self.get_bot_chat_list()
            for chat in chat_list:
                if chat['chat_id'] == chat_id:
                    return chat
            return None
        except Exception as e:
            logger.exception(f"根据chat_id获取群组信息时发生异常: {str(e)}")
            return None


# 创建全局实例
chat_list_service = ChatListService()


# 便捷函数
def get_bot_chat_list(page_size: int = 50) -> List[Dict[str, Any]]:
    """获取机器人所在的群组列表（便捷函数）"""
    return chat_list_service.get_bot_chat_list(page_size)


def get_group_chat_ids() -> List[str]:
    """获取所有群聊的chat_id列表（便捷函数）"""
    return chat_list_service.get_group_chat_ids()


def get_chat_info_by_id(chat_id: str) -> Optional[Dict[str, Any]]:
    """根据chat_id获取群组信息（便捷函数）"""
    return chat_list_service.get_chat_info_by_id(chat_id)
