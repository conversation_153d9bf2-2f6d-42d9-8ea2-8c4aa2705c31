"""
飞书部门服务模块
负责处理部门信息获取和管理
"""

import requests
from typing import Optional, List, Dict, Any

from src.utils.logger import logger


class DepartmentService:
    """飞书部门服务类"""
    
    @staticmethod
    def _safe_parse_feishu_response(response, expected_data_key: str = None, context: str = ""):
        """安全解析飞书API响应
        
        Args:
            response: HTTP响应对象
            expected_data_key: 期望的data中的键名
            context: 上下文信息，用于日志
            
        Returns:
            tuple: (success: bool, data: Any, error_msg: str)
        """
        try:
            if response.status_code != 200:
                error_msg = f"HTTP请求失败: status={response.status_code}, context={context}"
                logger.error(error_msg)
                return False, None, error_msg
            
            result = response.json()
            logger.info(f"飞书API响应: context={context}, result={result}")
            
            if result.get("code") != 0:
                error_msg = f"飞书API返回错误: code={result.get('code')}, msg={result.get('msg', '')}, context={context}"
                logger.error(error_msg)
                return False, None, error_msg
            
            data = result.get("data", {})
            if not isinstance(data, dict):
                error_msg = f"API响应data字段格式异常: data={data}, context={context}"
                logger.error(error_msg)
                return False, None, error_msg
            
            if expected_data_key:
                if expected_data_key not in data:
                    error_msg = f"API响应缺少期望字段: expected_key={expected_data_key}, available_keys={list(data.keys())}, context={context}"
                    logger.error(error_msg)
                    return False, None, error_msg
                return True, data[expected_data_key], ""
            
            return True, data, ""
            
        except Exception as e:
            error_msg = f"解析飞书API响应时出错: error={e}, context={context}"
            logger.error(error_msg, exc_info=True)
            return False, None, error_msg
    
    @staticmethod
    def get_department_parents(department_id: str) -> Optional[List[Dict[str, Any]]]:
        """获取部门的所有父级部门

        Args:
            department_id: 部门ID (open_department_id格式)

        Returns:
            Optional[List[Dict[str, Any]]]: 父级部门列表，失败时返回None
        """
        try:
            # 获取tenant token
            from src.services.feishu.token_service import TokenService
            tenant_token = TokenService.get_tenant_access_token()

            if not tenant_token:
                logger.error(f"无法获取tenant token: department_id={department_id}")
                return None

            # 构造请求URL和参数
            url = "https://open.feishu.cn/open-apis/contact/v3/departments/parent"
            params = {
                "department_id": department_id,
                "department_id_type": "open_department_id",
                "page_size": 20,
                "user_id_type": "user_id"
            }
            headers = {"Authorization": f"Bearer {tenant_token}"}

            logger.info(f"调用飞书部门父级API: department_id={department_id}")

            # 发起HTTP请求
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            # 使用安全解析方法
            success, data, error_msg = DepartmentService._safe_parse_feishu_response(
                response, 
                context=f"get_department_parents(department_id={department_id})"
            )
            
            if not success:
                logger.error(f"获取部门父级信息失败: department_id={department_id}, error={error_msg}")
                return None
            
            # 安全地获取 items，如果不存在则返回空列表
            departments = data.get("items", [])
            logger.info(f"成功获取部门父级信息: department_id={department_id}, 父级部门数量={len(departments)}")
            return departments

        except Exception as e:
            logger.error(f"获取部门父级信息时出错: department_id={department_id}, error={e}", exc_info=True)
            return None
    
    @staticmethod
    def get_user_first_level_department(open_id: str) -> Optional[str]:
        """获取用户的一级部门名称

        Args:
            open_id: 用户的open_id

        Returns:
            Optional[str]: 一级部门名称，获取失败时返回None
        """
        try:
            logger.info(f"开始获取用户一级部门: open_id={open_id}")

            # 直接使用tenant token获取用户信息
            user_info = DepartmentService._get_user_info_with_tenant_token(open_id)
            if not user_info:
                logger.error(f"获取用户信息失败: open_id={open_id}")
                return None

            logger.info(f"成功获取用户信息: open_id={open_id}")

            department_ids = user_info.get("department_ids", [])
            if not department_ids:
                logger.info(f"用户没有部门信息: open_id={open_id}, user_name={user_info.get('name', 'Unknown')}")
                return None
            
            # 取第一个部门ID
            first_department_id = department_ids[0]
            logger.info(f"用户的第一个部门ID: open_id={open_id}, department_id={first_department_id}")
            
            # 获取该部门的所有父级部门
            parent_departments = DepartmentService.get_department_parents(first_department_id)
            
            if parent_departments is None:
                logger.error(f"获取部门父级信息失败: department_id={first_department_id}")
                return None
            
            if not parent_departments:
                # 如果没有父级部门，说明当前部门就是顶级部门
                # 需要获取当前部门的信息
                current_dept_info = DepartmentService.get_department_info(first_department_id)
                if current_dept_info:
                    first_level_dept_name = current_dept_info.get("name", "")
                    logger.info(f"用户部门本身就是一级部门: open_id={open_id}, department={first_level_dept_name}")
                    return first_level_dept_name
                else:
                    logger.error(f"获取当前部门信息失败: department_id={first_department_id}")
                    return None
            
            # 取最后一个父级部门作为一级部门
            first_level_department = parent_departments[-1]
            first_level_dept_name = first_level_department.get("name", "")
            
            logger.info(f"成功获取用户一级部门: open_id={open_id}, first_level_department={first_level_dept_name}")
            return first_level_dept_name
            
        except Exception as e:
            logger.error(f"获取用户一级部门时出错: open_id={open_id}, error={e}", exc_info=True)
            return None
    
    @staticmethod
    def get_department_info(department_id: str) -> Optional[Dict[str, Any]]:
        """获取单个部门的详细信息

        Args:
            department_id: 部门ID (open_department_id格式)

        Returns:
            Optional[Dict[str, Any]]: 部门信息，失败时返回None
        """
        try:
            # 获取tenant token
            from src.services.feishu.token_service import TokenService
            tenant_token = TokenService.get_tenant_access_token()

            if not tenant_token:
                logger.error(f"无法获取tenant token: department_id={department_id}")
                return None

            # 构造请求URL和参数
            url = f"https://open.feishu.cn/open-apis/contact/v3/departments/{department_id}"
            params = {
                "department_id_type": "open_department_id",
                "user_id_type": "user_id"
            }
            headers = {"Authorization": f"Bearer {tenant_token}"}

            logger.info(f"调用飞书部门信息API: department_id={department_id}")

            # 发起HTTP请求
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            # 使用安全解析方法
            success, dept_data, error_msg = DepartmentService._safe_parse_feishu_response(
                response, 
                expected_data_key="department",
                context=f"get_department_info(department_id={department_id})"
            )
            
            if not success:
                logger.error(f"获取部门信息失败: department_id={department_id}, error={error_msg}")
                return None
            
            logger.info(f"成功获取部门信息: department_id={department_id}")
            return dept_data

        except Exception as e:
            logger.error(f"获取部门信息时出错: department_id={department_id}, error={e}", exc_info=True)
            return None



    @staticmethod
    def _get_user_info_with_tenant_token(open_id: str) -> Optional[Dict[str, Any]]:
        """使用tenant token获取用户信息

        Args:
            open_id: 用户的open_id

        Returns:
            Optional[Dict[str, Any]]: 用户信息字典，获取失败时返回None
        """
        try:
            # 获取tenant token
            from src.services.feishu.token_service import TokenService
            tenant_token = TokenService.get_tenant_access_token()

            if not tenant_token:
                logger.warning(f"无法获取tenant token: open_id={open_id}")
                return None

            url = f"https://open.feishu.cn/open-apis/contact/v3/users/{open_id}?department_id_type=open_department_id&user_id_type=open_id"
            headers = {"Authorization": f"Bearer {tenant_token}"}

            logger.info(f"使用tenant token调用飞书用户信息API: open_id={open_id}")

            response = requests.get(url, headers=headers, timeout=10)
            
            # 使用安全解析方法
            success, user_data, error_msg = DepartmentService._safe_parse_feishu_response(
                response, 
                expected_data_key="user",
                context=f"_get_user_info_with_tenant_token(open_id={open_id})"
            )
            
            if not success:
                logger.error(f"使用tenant token获取用户信息失败: open_id={open_id}, error={error_msg}")
                return None
            
            logger.info(f"使用tenant token成功获取用户信息: open_id={open_id}, department_ids={user_data.get('department_ids', [])}")
            return user_data

        except Exception as e:
            logger.error(f"使用tenant token获取用户信息时出错: open_id={open_id}, error={e}", exc_info=True)
            return None
