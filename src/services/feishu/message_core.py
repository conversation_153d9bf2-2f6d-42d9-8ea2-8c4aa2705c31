"""
飞书消息发送核心功能模块
"""
import json
import uuid
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET

# 创建lark客户端
lark_client = (
    lark.Client.builder()
    .app_id(APP_ID)
    .app_secret(APP_SECRET)
    .timeout(3)
    .log_level(lark.LogLevel.INFO)
    .build()
)

def send_feishu_message(
    *,
    message_id: str = None,
    chat_id: str = None,
    content: str,
    msg_type: str = "text",
    reply_in_thread: bool = False,
    uuid_str: str = None,
    receive_id_type: str = "chat_id"
) -> bool:
    """通用的飞书消息发送函数，支持文本、交互式、卡片等类型

    Args:
        message_id (str): 回复消息ID（用于reply）
        chat_id (str): 群聊ID（用于create）
        content (str): 消息内容（JSON字符串或文本）
        msg_type (str): 消息类型，text/interactive/card_json等
        reply_in_thread (bool): 是否线程回复
        uuid_str (str): 消息唯一ID
        receive_id_type (str): 群聊类型，默认chat_id

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    uuid_str = uuid_str or str(uuid.uuid4())
    try:
        if message_id:
            # 回复消息
            req = (
                ReplyMessageRequest.builder()
                .message_id(message_id)
                .request_body(
                    ReplyMessageRequestBody.builder()
                    .content(content)
                    .msg_type(msg_type)
                    .reply_in_thread(reply_in_thread)
                    .uuid(uuid_str)
                    .build()
                )
                .build()
            )
            logger.info(f"发送reply消息到 {message_id}: {content[:100]}...")
            resp = lark_client.im.v1.message.reply(request=req)
        elif chat_id:
            # 发送新消息到群聊
            req = (
                CreateMessageRequest.builder()
                .receive_id_type(receive_id_type)
                .request_body(
                    CreateMessageRequestBody.builder()
                    .receive_id(chat_id)
                    .content(content)
                    .msg_type(msg_type)
                    .uuid(uuid_str)
                    .build()
                )
                .build()
            )
            logger.info(f"发送新消息到chat_id {chat_id}: {content[:100]}...")
            resp = lark_client.im.v1.message.create(request=req)
        else:
            logger.error("send_feishu_message: message_id和chat_id不能同时为空")
            return False

        if resp.success():
            logger.info(f"消息发送成功: {resp.raw.content.decode('utf-8')}")
            return True
        else:
            logger.error(f"消息发送失败: {resp.raw.content.decode('utf-8')}")
            return False
    except Exception as e:
        logger.error(f"发送飞书消息异常: {str(e)}", exc_info=True)
        return False

def reply_simple_text_message(message_id: str, text: str):
    """回复简单文本消息"""
    return send_feishu_message(
        message_id=message_id,
        content=json.dumps({"text": text}),
        msg_type="text",
        reply_in_thread=False
    )

def reply_interactive_message(message_id: str, content: str) -> bool:
    """回复交互式消息（如卡片）"""
    return send_feishu_message(
        message_id=message_id,
        content=content,
        msg_type="interactive",
        reply_in_thread=False
    )

def send_message_to_chat(chat_id: str, message: str, msg_type: str = "text") -> bool:
    """
    发送消息到指定的飞书群聊

    Args:
        chat_id (str): 群聊ID
        message (str): 要发送的消息内容
        msg_type (str): 消息类型，默认为"text"

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not chat_id:
        logger.warning("群聊ID为空，无法发送消息")
        return False

    # 文本消息需转为json格式
    content = json.dumps({"text": message}) if msg_type == "text" else message
    return send_feishu_message(
        chat_id=chat_id,
        content=content,
        msg_type=msg_type
    )
