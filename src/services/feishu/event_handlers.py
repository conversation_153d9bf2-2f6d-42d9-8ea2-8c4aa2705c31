"""
飞书事件处理器模块
负责处理飞书的各种事件，包括消息接收和卡片操作
"""

import json
import asyncio
import time
import os
import concurrent.futures
import lark_oapi as lark
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerResponse,
)
from src.utils.logger import logger
# mark_bad_case 现在通过动态导入使用
from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.services.chatbot.history_service import (
    get_user_latest_queries,
    get_other_users_latest_queries,
)
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    get_message_image_content,
)
from .deduplicator import message_deduplicator
from .user_service import UserService
from .message_parser import MessageParser
from .query_processor import QueryProcessor
from .notification_sender import send_recommendation_card
from .action_handlers_config import get_action_config
from src.config.concurrency_config import ConcurrencyConfig


class EventHandlers:
    """事件处理器类"""

    # 为消息处理创建专用线程池，避免阻塞飞书WebSocket连接
    _message_executor = concurrent.futures.ThreadPoolExecutor(
        max_workers=ConcurrencyConfig.FEISHU_MESSAGE_THREAD_POOL_SIZE,
        thread_name_prefix="feishu_message_worker"
    )

    # 注意：卡片操作处理器配置现在从 action_handlers_config.py 中获取

    @staticmethod
    def _get_user_name_from_data(data: P2CardActionTrigger) -> str:
        """从卡片操作数据中获取用户名

        Args:
            data: 卡片操作触发数据

        Returns:
            str: 用户名，获取失败时返回默认值
        """
        try:
            user_id = data.event.operator.open_id
            if user_id:
                user_info_str = UserService.get_user_info(user_id)
                if user_info_str:
                    user_info = json.loads(user_info_str)
                    return user_info.get("name", "飞书用户")
        except Exception as e:
            logger.warning(f"获取用户信息失败: {e}")
        return "飞书用户"

    @staticmethod
    def _execute_action_service(action_config: dict, conversation_id: str, user_name: str) -> None:
        """执行具体的业务服务操作

        Args:
            action_config: 操作配置
            conversation_id: 对话ID
            user_name: 用户名
        """
        # 动态导入服务模块
        import importlib
        service_module = importlib.import_module(action_config["service_module"])
        service_function = getattr(service_module, action_config["service_function"])

        # 准备参数
        params = {
            "conversation_id": conversation_id,
            "user_name": user_name,
            **action_config["service_params"]
        }

        # 执行服务操作
        service_function(**params)

    @staticmethod
    def _schedule_after_action(action_config: dict, card_id: str) -> None:
        """调度后续操作任务

        Args:
            action_config: 操作配置
            card_id: 卡片ID
        """
        if not card_id:
            logger.warning(f"card_id 为空，无法调度 {action_config['after_action_function']} 的延迟调用")
            return

        # 动态导入后续操作模块
        import importlib
        after_action_module = importlib.import_module(action_config["after_action_module"])
        after_action_function = getattr(after_action_module, action_config["after_action_function"])

        # 定义异步任务
        async def _delayed_after_action_task(current_card_id: str):
            await asyncio.sleep(1.5)  # 异步等待1.5秒
            await asyncio.to_thread(after_action_function, current_card_id)

        # 创建后台任务
        asyncio.create_task(_delayed_after_action_task(card_id))

    @staticmethod
    def handle_card_action_trigger(
        data: P2CardActionTrigger,
    ) -> P2CardActionTriggerResponse:
        """处理卡片按钮点击回调

        Args:
            data: 卡片操作触发数据

        Returns:
            P2CardActionTriggerResponse: 响应对象
        """
        logger.info(lark.JSON.marshal(data))
        conversation_id = data.event.action.value.get("conversation_id")
        card_id = data.event.action.value.get("card_id")
        action = data.event.action.value.get("action", "bad_case")  # 默认为 bad_case 以保持向后兼容

        logger.info(f"用户准备将 conversation_id 标记为 {action}:{conversation_id}")

        if not conversation_id:
            return P2CardActionTriggerResponse({
                "toast": {
                    "type": "error",
                    "content": "操作失败，请稍后重试！",
                },
            })

        # 检查操作是否支持
        action_config = get_action_config(action)
        if not action_config:
            logger.warning(f"不支持的操作类型: {action}")
            return P2CardActionTriggerResponse({
                "toast": {
                    "type": "error",
                    "content": f"不支持的操作类型: {action}",
                },
            })

        try:
            # 获取用户信息
            user_name = EventHandlers._get_user_name_from_data(data)

            # 执行业务操作
            EventHandlers._execute_action_service(action_config, conversation_id, user_name)

            # 调度后续操作
            EventHandlers._schedule_after_action(action_config, card_id)

            # 返回成功响应
            return P2CardActionTriggerResponse({
                "toast": action_config["toast"]
            })

        except Exception as e:
            logger.error(f"处理卡片操作时出错: {e}", exc_info=True)
            return P2CardActionTriggerResponse({
                "toast": {
                    "type": "error",
                    "content": "操作失败，请稍后重试！",
                },
            })

    @staticmethod
    async def handle_message_receive(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """处理接收到的飞书消息

        Args:
            data: 消息接收数据
        """
        message_data_str = lark.JSON.marshal(data)
        logger.info(f"Async handler processing message: {message_data_str}")

        # 基本验证
        if not data.event.message.content:
            logger.info(f"无需处理: 消息内容为空")
            return

        # 解析消息内容
        content_data = MessageParser.parse_message_content(data.event.message.content)

        # 提取文本内容
        user_query = MessageParser.extract_text_from_content(content_data)
        logger.info(f"提取到的文本内容: {user_query}")

        message_id = data.event.message.message_id
        image_key = MessageParser.extract_image_key_from_content(content_data)
        image_url = None
        if image_key:
            logger.info(f"获取消息图片内容: {message_id}, {image_key}")
            image_url = get_message_image_content(message_id, image_key)
            if image_url:
                if len(image_url) > 60 and not image_url.startswith("data:image/jpeg;base64,"):
                    image_url = f"data:image/jpeg;base64,{image_url}"
                else:
                    logger.warning(f"非法的 image_url：{image_url}")
                    image_url = None

        # 获取聊天类型和提及信息
        chat_type = data.event.message.chat_type
        mentions = getattr(data.event.message, "mentions", None)

        # 如果有提及信息，将 user_query 中的 @user_1 替换为实际的人名
        if mentions:
            mention_map = {}
            for mention in mentions:
                try:
                    mention_key = mention.key if hasattr(mention, "key") else mention["key"]
                    mention_name = mention.name if hasattr(mention, "name") else mention["name"]
                    mention_map[mention_key] = mention_name
                except (KeyError, AttributeError) as e:
                    logger.warning(f"提及信息解析失败: {mention}, 错误: {e}")
            for mention_key, mention_name in mention_map.items():
                user_query = user_query.replace(mention_key, f"@{mention_name}")
            logger.info(f"替换提及后的文本内容: {user_query}")

        # 检查是否应该跳过处理
        should_skip, skip_reason = MessageParser.should_skip_message(
            content_data, user_query, chat_type, mentions
        )
        if should_skip:
            logger.info(f"跳过处理: {skip_reason}")
            return

        parent_id = getattr(data.event.message, "parent_id", None)
        root_id = getattr(data.event.message, "root_id", None) or message_id
        chat_id = data.event.message.chat_id

        # 消息去重检查
        if message_deduplicator.is_message_processed(message_id):
            return
        else:
            message_deduplicator.mark_message_processed(message_id)

        # 检查发送者类型
        if data.event.sender.sender_type != "user":
            logger.info(f"忽略非用户消息: sender_type={data.event.sender.sender_type}")
            return

        user_open_id = data.event.sender.sender_id.open_id
        if not user_open_id:
            logger.info(f"无需处理: 无法获取 user_open_id")
            return

        # 获取用户信息
        user_info_str = UserService.get_user_info(user_id=user_open_id)
        if not user_info_str:
            logger.info(f"获取用户失败: user_open_id:{user_open_id}")
            reply_simple_text_message(
                message_id, "抱歉，无法获取您的用户信息，请稍后再试。"
            )
            return

        user_info_dict = json.loads(user_info_str)
        logger.info(f"获取用户成功:{user_info_dict}")
        
        # 添加open_id到用户信息字典中，用于后续获取access_token
        user_info_dict["open_id"] = user_open_id

        # 保存用户信息到数据库
        UserService.upsert_user_info_to_db(user_info_dict, user_open_id)

        # 处理用户信息
        user_info_dict = UserService.process_user_info(user_info_dict)

        if not user_query:
            logger.info(f"无需处理: 消息文本为空")
            reply_simple_text_message(message_id, "请输入您的问题。")
            return

        # 清理用户查询
        user_query = MessageParser.clean_user_query(user_query)

        # 验证查询长度（仅对新对话）
        is_new_conversation = root_id == message_id
        if not MessageParser.validate_query_length(user_query, is_new_conversation):
            logger.info(f"用户消息过短: {user_query}")
            # 使用推荐机器人生成推荐问题
            await EventHandlers._handle_short_query_with_recommendations(
                message_id=message_id,
                user_info_dict=user_info_dict,
                chat_id=chat_id
            )
            return

        # 处理查询
        try:
            # 直接等待查询处理完成，而不是创建异步任务
            await QueryProcessor.handle_agent_query(
                message_id=message_id,
                user_query=user_query.strip(),
                user_info_dict=user_info_dict,
                root_id=root_id,
                parent_id=parent_id,
                image_url=image_url,
            )
        except Exception as e:
            logger.error(f"处理Agent查询时出错: {e}", exc_info=True)
            reply_simple_text_message(message_id, f"处理您的请求时遇到错误: {e}")
            
    @staticmethod
    async def handle_user_entered(data: lark.im.v1.P2ImChatAccessEventBotP2pChatEnteredV1) -> None:
        """处理用户进入P2P聊天事件

        Args:
            data: 用户进入事件数据
        """
        try:
            # 获取当前时间的时间戳（秒）
            current_time = time.time()
            # 将事件的时间戳从毫秒转换为秒
            last_message_create_time = int(data.event.last_message_create_time) / 1000
            
            user_open_id = data.event.operator_id.open_id
            # 导入用户授权服务
            from src.services.feishu.user_auth_service import UserAuthService
            
            # 首先检查用户是否需要授权提醒
            if UserAuthService.should_send_auth_reminder(user_open_id):
                # 发送授权提醒消息
                if current_time - last_message_create_time > 20*60:
                    auth_sent = UserAuthService.send_auth_reminder(data.event.chat_id)
                    if auth_sent:
                        logger.info(f"已向用户 ({user_open_id}) 发送授权提醒")
                        return  # 发送授权提醒后直接返回，不继续处理推荐
                    else:
                        logger.warning(f"向用户 ({user_open_id}) 发送授权提醒失败")
                else:
                    logger.info(f"无需向用户 ({user_open_id}) 发送授权提醒，因为距离上次消息不足20分钟")
            
            # 配置的时间间隔（默认12小时，单位为秒）
            time_threshold = int(os.getenv("USER_ENTERED_THRESHOLD", "12")) * 3600
            
            data_str = lark.JSON.marshal(data)
            logger.info(f"Async handler processing user entered: {data_str}")

            # 检查事件是否早于当前时间time_threshold小时
            if current_time - last_message_create_time < time_threshold:
                logger.info(f"无需处理用户进入事件: {data.event.last_message_create_time}, 时间间隔为{time_threshold}秒")
                return

            user_info_str = UserService.get_user_info(user_open_id)
            if not user_info_str:
                logger.error(f"获取用户失败: user_open_id:{user_open_id}")
                return
            
            user_info = json.loads(user_info_str)
            user_email = user_info.get("email")
            if not user_email:
                logger.error(f"用户邮箱为空，无法生成推荐: user_open_id:{user_open_id}")
                return

            # 1. 获取当前用户的历史消息
            current_user_messages = get_user_latest_queries(user_email=user_email, limit=10)
            logger.info(f"为用户 {user_email} 获取到 {len(current_user_messages)} 条历史消息")

            # 2. 获取其他用户的历史消息
            other_users_messages = get_other_users_latest_queries(current_user_email=user_email, limit=10)
            logger.info(f"为用户 {user_email} 获取到 {len(other_users_messages)} 条其他用户消息作为参考")

            # 如果没有任何历史消息，则不进行推荐
            if not current_user_messages and not other_users_messages:
                logger.info(f"用户 {user_email} 没有任何历史消息，不进行推荐")
                return

            # 3. 调用推荐机器人
            recommendation_bot = UserQueryRecommendationBot(user_info)
            recommendations = await recommendation_bot.get_recommendations(
                current_user_messages=current_user_messages,
                other_users_messages=other_users_messages,
            )

            # 4. 发送推荐卡片
            if recommendations:
                logger.info(f"成功为用户 {user_email} 生成 {len(recommendations)} 条推荐，准备发送卡片")
                await send_recommendation_card(
                    chat_id=data.event.chat_id,
                    user_name=user_info.get("name", "飞书用户"),
                    recommendations=recommendations
                )
            else:
                logger.info(f"未能为用户 {user_email} 生成推荐问题")
        except Exception as e:
            logger.exception(f"处理用户进入事件时出错: {e}")
            return

    @staticmethod
    async def _handle_short_query_with_recommendations(
        message_id: str,
        user_info_dict: dict,
        chat_id: str
    ) -> None:
        """处理过短查询，使用推荐机器人生成推荐问题

        Args:
            message_id: 消息ID
            user_info_dict: 用户信息字典
            chat_id: 聊天ID
        """
        try:
            user_email = user_info_dict.get("email")
            if not user_email:
                logger.error(f"用户邮箱为空，无法生成推荐: message_id:{message_id}")
                # 回退到原来的简单回复
                reply_text = MessageParser.get_query_too_short_reply()
                reply_simple_text_message(message_id, reply_text)
                return

            # 1. 获取当前用户的历史消息
            current_user_messages = get_user_latest_queries(user_email=user_email, limit=10)
            logger.info(f"为用户 {user_email} 获取到 {len(current_user_messages)} 条历史消息")

            # 2. 获取其他用户的历史消息
            other_users_messages = get_other_users_latest_queries(current_user_email=user_email, limit=10)
            logger.info(f"为用户 {user_email} 获取到 {len(other_users_messages)} 条其他用户消息作为参考")

            # 如果没有任何历史消息，则使用原来的简单回复
            if not current_user_messages and not other_users_messages:
                logger.info(f"用户 {user_email} 没有任何历史消息，使用简单回复")
                reply_text = MessageParser.get_query_too_short_reply()
                reply_simple_text_message(message_id, reply_text)
                return

            # 3. 调用推荐机器人
            recommendation_bot = UserQueryRecommendationBot(user_info_dict)
            recommendations = await recommendation_bot.get_recommendations(
                current_user_messages=current_user_messages,
                other_users_messages=other_users_messages,
            )

            # 4. 发送推荐卡片或简单回复
            if recommendations:
                logger.info(f"成功为用户 {user_email} 生成 {len(recommendations)} 条推荐，准备发送卡片")
                await send_recommendation_card(
                    chat_id=chat_id,
                    user_name=user_info_dict.get("name", "飞书用户"),
                    recommendations=recommendations,
                    message_id=message_id
                )
            else:
                logger.info(f"未能为用户 {user_email} 生成推荐问题，使用简单回复")
                reply_text = MessageParser.get_query_too_short_reply()
                reply_simple_text_message(message_id, reply_text)

        except Exception as e:
            logger.exception(f"处理短查询推荐时出错: {e}")
            # 出错时回退到原来的简单回复
            reply_text = MessageParser.get_query_too_short_reply()
            reply_simple_text_message(message_id, reply_text)

    @staticmethod
    def sync_wrapper_message_receive(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """同步包装器，用于调度异步消息处理器

        Args:
            data: 消息接收数据
        """
        try:
            message_id = data.event.message.message_id
            logger.info(f"同步包装器收到消息: {message_id}")

            # 将消息处理提交到专用线程池，避免阻塞飞书WebSocket连接
            EventHandlers._message_executor.submit(
                EventHandlers._process_message_in_thread, data
            )
            logger.debug(f"消息 {message_id} 已提交到线程池处理")

        except Exception as e:
            logger.error(f"调度消息处理任务时出错: {e}", exc_info=True)

    @staticmethod
    def _process_message_in_thread(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """在线程中处理消息（创建新的事件循环）"""
        try:
            # 在线程中创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 运行异步消息处理
                loop.run_until_complete(EventHandlers.handle_message_receive(data))
            finally:
                # 确保事件循环被正确关闭
                loop.close()

        except Exception as e:
            logger.error(f"线程中处理消息时出错: {e}", exc_info=True)

    @staticmethod
    def sync_wrapper_p2p_user_entered(data: lark.im.v1.P2ImChatAccessEventBotP2pChatEnteredV1) -> None:
        """同步包装器，用于接收P2P用户进入事件

        Args:
            data: 消息接收数据
        """
        try:
            chat_id = data.event.chat_id
            logger.info(f"用户进入了和机器人的P2P聊天: chat_id:{chat_id}, last_message_create_time:{data.event.last_message_create_time}")

            # 将用户进入事件处理提交到专用线程池
            EventHandlers._message_executor.submit(
                EventHandlers._process_user_entered_in_thread, data
            )
            logger.debug(f"用户进入事件 {chat_id} 已提交到线程池处理")

        except Exception as e:
            logger.error(f"调度用户进入事件处理任务时出错: {e}", exc_info=True)

    @staticmethod
    def _process_user_entered_in_thread(data: lark.im.v1.P2ImChatAccessEventBotP2pChatEnteredV1) -> None:
        """在线程中处理用户进入事件（创建新的事件循环）"""
        try:
            # 在线程中创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 运行异步用户进入事件处理
                loop.run_until_complete(EventHandlers.handle_user_entered(data))
            finally:
                # 确保事件循环被正确关闭
                loop.close()

        except Exception as e:
            logger.error(f"线程中处理用户进入事件时出错: {e}", exc_info=True)

def create_event_handler():
    """创建事件处理器

    Returns:
        事件处理器实例
    """
    return (
        lark.EventDispatcherHandler.builder("", "")
        .register_p2_im_message_receive_v1(EventHandlers.sync_wrapper_message_receive)
        .register_p2_card_action_trigger(EventHandlers.handle_card_action_trigger)
        .register_p2_im_chat_access_event_bot_p2p_chat_entered_v1(EventHandlers.sync_wrapper_p2p_user_entered)
        .build()
    )
