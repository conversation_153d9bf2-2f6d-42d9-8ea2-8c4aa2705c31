"""
Agent module for handling user queries and generating responses.

This module is organized into three main components:
1. bots - Different bot implementations (data_fetcher, qa_bot, master_controller, etc.)
2. tools - Shared tools used by bots
3. utils - Utility functions for agent module
"""

# Import main components for easy access
from src.services.agent.runner import run_agent_query
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.services.agent.bots.coordinator_bot import CoordinatorBot
# 保留原有的MasterControllerBot以备兼容性需要
from src.services.agent.bots.master_controller_bot import MasterControllerBot

__all__ = [
    'run_agent_query',
    'DataFetcherBot',
    'CoordinatorBot',
    'MasterControllerBot',
]