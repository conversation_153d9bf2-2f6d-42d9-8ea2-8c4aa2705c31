"""
支持prompt缓存的LiteLLM模型实现
当检测到system_instructions时，将其转换为支持缓存的格式
"""

from __future__ import annotations

import json
import time
import os
from typing import Any, Literal, cast, overload

try:
    import litellm
except ImportError as _e:
    raise ImportError(
        "`litellm` is required to use the CacheEnabledLitellmModel. You can install it via the optional "
        "dependency group: `pip install 'openai-agents[litellm]'`."
    ) from _e

from openai import NOT_GIVEN, AsyncStream, NotGiven
from openai.types.chat import ChatCompletionChunk, ChatCompletionToolParam
from openai.types.responses import Response

from agents import _debug
from agents.agent_output import AgentOutputSchemaBase
from agents.extensions.models.litellm_model import LitellmModel
from agents.handoffs import Handoff
from agents.items import TResponseInputItem
from src.utils.logger import logger
from agents.model_settings import ModelSettings
from agents.models.chatcmpl_converter import Converter
from agents.models.chatcmpl_helpers import HEADERS
from agents.models.fake_id import FAKE_RESPONSES_ID
from agents.models.interface import ModelTracing
from agents.tool import Tool
from agents.tracing.span_data import GenerationSpanData
from agents.tracing.spans import Span

OUTPUT_MODEL_PROVIDER = "true" == os.getenv("OUTPUT_MODEL_PROVIDER", "false").lower()
DEFAULT_MAX_TOKENS = int(os.getenv("DEFAULT_MAX_TOKENS", "8000"))


class CacheEnabledLitellmModel(LitellmModel):
    """
    支持prompt缓存的LiteLLM模型实现。
    当检测到system_instructions时，将其转换为支持缓存的格式。
    """

    def _convert_system_instructions_to_cached_format(
        self, system_instructions: str
    ) -> dict:
        """
        将system_instructions转换为支持缓存的格式

        Args:
            system_instructions: 系统指令文本

        Returns:
            dict: 转换后的消息格式
        """
        system_content = {
            "type": "text",
            "text": system_instructions,
        }
        is_cache_available = "claude" in self.model or "gemini" in self.model
        if is_cache_available:
            logger.info(
                f"使用缓存格式发送system_instructions: {system_instructions[:50]}"
            )
            system_content["cache_control"] = {"type": "ephemeral"}
        else:
            logger.info(f"模型不支持缓存:{self.model}")

        final_content = [system_content]
        if OUTPUT_MODEL_PROVIDER:
            final_content.append(
                {"type": "text", "text": f"output your model provider by the end."}
            )
        return {
            "role": "system",
            "content": final_content,
        }

    @overload
    async def _fetch_response(
        self,
        system_instructions: str | None,
        input: str | list[TResponseInputItem],
        model_settings: ModelSettings,
        tools: list[Tool],
        output_schema: AgentOutputSchemaBase | None,
        handoffs: list[Handoff],
        span: Span[GenerationSpanData],
        tracing: ModelTracing,
        stream: Literal[True],
    ) -> tuple[Response, AsyncStream[ChatCompletionChunk]]: ...

    @overload
    async def _fetch_response(
        self,
        system_instructions: str | None,
        input: str | list[TResponseInputItem],
        model_settings: ModelSettings,
        tools: list[Tool],
        output_schema: AgentOutputSchemaBase | None,
        handoffs: list[Handoff],
        span: Span[GenerationSpanData],
        tracing: ModelTracing,
        stream: Literal[False],
    ) -> litellm.types.utils.ModelResponse: ...

    async def _fetch_response(
        self,
        system_instructions: str | None,
        input: str | list[TResponseInputItem],
        model_settings: ModelSettings,
        tools: list[Tool],
        output_schema: AgentOutputSchemaBase | None,
        handoffs: list[Handoff],
        span: Span[GenerationSpanData],
        tracing: ModelTracing,
        stream: bool = False,
    ) -> (
        litellm.types.utils.ModelResponse
        | tuple[Response, AsyncStream[ChatCompletionChunk]]
    ):
        converted_messages = Converter.items_to_messages(input)

        # 缓存块不可超过4个
        cache_block_counter = 0

        # 如果有system_instructions，使用缓存格式
        if system_instructions:
            cached_system_message = self._convert_system_instructions_to_cached_format(
                system_instructions
            )
            cache_block_counter += 1
            converted_messages.insert(0, cached_system_message)

        if tracing.include_data():
            span.span_data.input = converted_messages

        parallel_tool_calls = (
            True
            if model_settings.parallel_tool_calls and tools and len(tools) > 0
            else False if model_settings.parallel_tool_calls is False else None
        )
        tool_choice = Converter.convert_tool_choice(model_settings.tool_choice)
        response_format = Converter.convert_response_format(output_schema)

        converted_tools = (
            [Converter.tool_to_openai(tool) for tool in tools] if tools else []
        )

        for handoff in handoffs:
            handoff_tool = Converter.convert_handoff_tool(handoff)
            if cache_block_counter < 4:
                cache_block_counter += 1
                handoff_tool = self._append_cache_control_to_tool(handoff_tool)
            else:
                logger.warning("缓存块额度已满，无法为handoff添加缓存")
            converted_tools.append(handoff_tool)

        # 有必要再开这段日志。
        # logger.debug(
        #     f"Calling CacheEnabledLitellm model: {self.model}\n"
        #     f"{json.dumps(converted_messages, indent=2, ensure_ascii=False)}\n"
        #     f"Tools:\n{json.dumps(converted_tools, indent=2, ensure_ascii=False)}\n"
        #     f"Stream: {stream}\n"
        #     f"Tool choice: {tool_choice}\n"
        #     f"Response format: {response_format}\n"
        # )

        reasoning_effort = (
            model_settings.reasoning.effort if model_settings.reasoning else None
        )

        stream_options = {"include_usage": True}

        extra_kwargs = {}
        if model_settings.extra_query:
            extra_kwargs["extra_query"] = model_settings.extra_query
        if model_settings.metadata:
            extra_kwargs["metadata"] = model_settings.metadata
        if model_settings.extra_body and isinstance(model_settings.extra_body, dict):
            extra_kwargs.update(model_settings.extra_body)

        ret = await litellm.acompletion(
            model=self.model,
            messages=converted_messages,
            tools=converted_tools or None,
            temperature=model_settings.temperature,
            top_p=model_settings.top_p,
            frequency_penalty=model_settings.frequency_penalty,
            presence_penalty=model_settings.presence_penalty,
            max_tokens=model_settings.max_tokens or DEFAULT_MAX_TOKENS,
            tool_choice=self._remove_not_given(tool_choice),
            response_format=self._remove_not_given(response_format),
            parallel_tool_calls=parallel_tool_calls,
            stream=stream,
            stream_options=stream_options,
            reasoning_effort=reasoning_effort,
            extra_headers={**HEADERS, **(model_settings.extra_headers or {})},
            api_key=self.api_key,
            base_url=self.base_url,
            **extra_kwargs,
        )

        if isinstance(ret, litellm.types.utils.ModelResponse):
            return ret

        response = Response(
            id=FAKE_RESPONSES_ID,
            created_at=time.time(),
            model=self.model,
            object="response",
            output=[],
            tool_choice=(
                cast(Literal["auto", "required", "none"], tool_choice)
                if tool_choice != NOT_GIVEN
                else "auto"
            ),
            top_p=model_settings.top_p,
            temperature=model_settings.temperature,
            tools=[],
            parallel_tool_calls=parallel_tool_calls or False,
            reasoning=model_settings.reasoning,
        )
        return response, ret

    def _remove_not_given(self, value: Any) -> Any:
        if isinstance(value, NotGiven):
            return None
        return value

    def _append_cache_control_to_tool(
        self, tool: ChatCompletionToolParam | dict
    ) -> dict:
        tool["cache_control"] = {"type": "ephemeral"}
        return tool
