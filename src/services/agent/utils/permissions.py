"""
Permission handling for agent bots.
"""

import os
from src.services.xianmudb.query_service import execute_business_query
from typing import Dict, Any

from src.utils.logger import logger
from src.utils.in_memory_cache import in_memory_cache # 导入缓存装饰器

# Load system admin job titles from environment
system_admin_job_titles_str = os.getenv(
    "SYSTEM_ADMIN_JOB_TITLES", "创始人,技术负责人,后端,销售策划,前端,测试,数据开发,产品经理,运营"
)
system_admin_job_titles = set(
    title.strip() for title in system_admin_job_titles_str.split(",") if title.strip()
)


@in_memory_cache(expire_seconds=1200) # 应用缓存装饰器，缓存1200秒
def get_user_area_no_permissions(admin_id: str) -> str:
    """
    获取用户的区域权限

    Args:
        user_name: 用户名

    Returns:
        str: 逗号分隔的区域编号列表，表示用户有权访问的区域
    """
    sql = f"""SELECT
  admin.admin_id,
  admin.realname,
  concat(
    "运营服务区编号列表:[",
    GROUP_CONCAT(distinct admin_data_permission.permission_value),
    ']; 运营服务区名字:[',GROUP_CONCAT(distinct area.area_name),
    ']; 运营服务大区列表:[',
    GROUP_CONCAT(distinct large_area.large_area_name),
    ']'
  ) AS large_area_name_list
FROM
  admin
  JOIN admin_data_permission ON admin.admin_id = admin_data_permission.admin_id
  JOIN area ON admin_data_permission.permission_value = area.area_no
  JOIN large_area ON area.large_area_no = large_area.large_area_no
WHERE
  admin.admin_id = '{admin_id}'
  AND admin_data_permission.type = 1
GROUP BY
  admin.admin_id,
  admin.realname;"""
    permission_area_no_list = execute_business_query(sql)
    if permission_area_no_list.success and permission_area_no_list.data and len(
        permission_area_no_list.data
    ) > 0:
        return permission_area_no_list.data[0][2]
    elif not permission_area_no_list.success:
        logger.error(
            f"Error fetching area permissions for user {admin_id}: {permission_area_no_list.error}"
        )
        return ""
    else:
        logger.warning(f"No area permissions found for user {admin_id}")
        return ""


from typing import Dict, Any


def get_user_permission(user_info: Dict[str, Any]) -> str:
    """
    获取用户的权限描述。

    Args:
        user_info: 包含用户信息的字典

    Returns:
        str: 用户的权限描述
    """
    job_title = user_info.get("job_title", "未知职位")
    user_name = user_info.get("name", "未知用户")
    admin_id = user_info.get("admin_id")

    # 1. 构建用户基本信息描述
    job_title_display = job_title
    if job_title == "销售主管":
        job_title_display = f"{job_title}-M1"
    elif job_title == "销售经理":
        job_title_display = f"{job_title}-M2"

    user_description_parts = [f"用户名:{user_name}", f"职务:{job_title_display}"]
    if job_title in ["销售专员", "销售经理", "销售主管"]:
        user_description_parts.append(f"bd_id(crm_bd_org.bd_id):{admin_id}")
    
    user_description = ", ".join(user_description_parts)

    # 2. 根据职位确定权限详情
    permission_details = ""
    if job_title in system_admin_job_titles:
        permission_details = "为系统管理员，无数据权限限制，可访问所有的数据"
    elif job_title == "销售专员":
        if admin_id:
            permission_details = f"只可访问归属他的门店(也就是他的私海内的门店,即:`follow_up_relation`.`admin_id`={admin_id})的门店之数据)"
        else:
            logger.warning(f"销售专员 {user_name} 缺少 admin_id，无法应用特定权限。")
            permission_details = "无法确定其具体门店权限，因为缺少 admin_id。"
    elif "销售" in job_title:
        area_no_list = get_user_area_no_permissions(admin_id=admin_id)
        if area_no_list:
            permission_details = f"只可访问他所负责的区域(即:`area`.`area_no` in ({area_no_list}))的门店之数据"
        else:
            logger.warning(f"销售人员 {user_name} 未找到区域权限。")
            permission_details = "无法确定其具体区域权限，因为在admin_data_permission表中未找到相关记录。"
    else:
        # 其他职位的默认情况
        permission_details = "无特殊数据权限限制"

    user_permission_description = f"{user_description}, {permission_details}"
    logger.info(f"用户权限描述: {user_permission_description}")
    return user_permission_description
