"""
API查询处理器
继承BaseQueryProcessor，为API接口提供统一的查询处理功能
"""
import json
import queue

from typing import Generator

from src.services.agent.base_query_processor import (
    BaseQueryProcessor, QueryRequest, THREAD_POOL
)
from src.utils.logger import logger



class APIQueryProcessor(BaseQueryProcessor):
    """API查询处理器 - 继承BaseQueryProcessor复用核心逻辑"""
    
    def __init__(self):
        super().__init__(THREAD_POOL)
    
    async def process_stream_events(self, result, message_queue: queue.Queue, streaming_message_id: int = None) -> tuple:
        """API流事件处理逻辑 - 复用基类的统一处理"""
        # 直接使用基类的统一流事件处理逻辑
        return await super().process_stream_events(result, message_queue, streaming_message_id)
    
    def handle_message_output(self, message: dict) -> str:
        """实现API特有的消息输出格式"""
        return message.get("content", "")
    
    def run_query(self, user_query: str, user_info: dict = {}, access_token: str = None,
                  conversation_id: str = None, images: list = None, model_name: str = None) -> Generator:
        """
        API查询处理的主入口函数
        
        Args:
            user_query: 用户查询
            user_info: 用户信息字典
            access_token: 访问令牌
            conversation_id: 对话ID
            images: 图片列表
            model_name: 模型名称
            
        Returns:
            Generator: 流式响应生成器
        """
        # 创建查询请求对象
        request = QueryRequest(
            user_query=user_query,
            user_info=user_info,
            access_token=access_token,
            conversation_id=conversation_id,
            images=images,
            model_name=model_name
        )
        
        # 创建消息队列
        message_queue = queue.Queue()
        
        # 创建异步工作函数
        async_worker = self.create_async_worker(request, message_queue)
        
        # 在线程池中执行异步工作
        self.thread_pool.submit(async_worker)
        
        # 返回流响应生成器
        return self._generate_api_response(request, message_queue)
    
    def _generate_api_response(self, request: QueryRequest, message_queue: queue.Queue) -> Generator:
        """生成API流式响应"""
        interrupted = False

        try:
            while True:
                try:
                    # 设置超时避免无限等待
                    message = message_queue.get(timeout=600)
                except queue.Empty:
                    logger.warning("消息队列获取超时")
                    # 通知用户超时情况
                    yield "[data]:" + json.dumps({"type": "error", "content": "响应超时，请稍后重试"}, ensure_ascii=False) + "\n"
                    break

                if message is None:
                    # 流式更新架构下，最终结果已在基类中处理完成
                    logger.debug("API流式处理完成")
                    break

                if isinstance(message, dict):
                    # 过滤掉不能序列化的消息类型
                    msg_type = message.get("type")
                    if msg_type in ["bot_instance", "final_result", "used_agents", "time_spend"]:
                        # 这些消息类型包含不能序列化的对象，跳过
                        continue

                    # 转发可序列化的消息到客户端
                    try:
                        yield "[data]:" + json.dumps(message, ensure_ascii=False) + "\n"
                    except (TypeError, ValueError) as e:
                        logger.warning(f"消息序列化失败，跳过: {e}")
                        continue

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            interrupted = True
            logger.warning(f"API客户端断开连接: {e}")
        except Exception as e:
            logger.exception(f"API生成器错误: {e}")
        finally:
            if interrupted:
                # 处理客户端断开连接
                self.handle_client_disconnect(request)
    



# 创建全局API查询处理器实例
api_processor = APIQueryProcessor()


def run_agent_query(user_query: str, user_info: dict = {}, access_token: str = None,
                   conversation_id: str = None, images: list = None, model_name: str = None) -> Generator:
    """
    API查询处理的兼容性函数，保持与原runner.py的接口一致
    
    Args:
        user_query: 用户查询
        user_info: 用户信息字典
        access_token: 访问令牌
        conversation_id: 对话ID
        images: 图片列表
        model_name: 模型名称
        
    Returns:
        Generator: 流式响应生成器
    """
    return api_processor.run_query(
        user_query, user_info, access_token, conversation_id, images, model_name
    )


# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭API查询处理器...")
    BaseQueryProcessor.shutdown_gracefully()
    logger.info("API查询处理器优雅关闭完成")


# 注册优雅关闭函数
import atexit
atexit.register(shutdown_gracefully)
