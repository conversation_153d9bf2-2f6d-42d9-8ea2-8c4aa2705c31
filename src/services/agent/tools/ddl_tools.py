"""
DDL-related tools for agent bots.
"""

from typing import Union, List
from src.utils.logger import logger
from src.utils.resource_manager import load_resource
from src.services.agent.tools.tool_manager import tool_manager
from src.services.xianmudb.query_service import execute_business_query
import json

async def fetch_ddl_for_table(table_names: Union[str, List[str]]) -> str:
    """根据表名获取对应的DDL内容。

    Args:
        table_names: 表名或表名列表，例如 'orders' 或 ['orders', 'products', 'merchant']。

    Returns:
        包含表的DDL语句。如果找不到对应的DDL文件，则返回错误信息。
    """
    # 如果传入的是字符串，转换为列表
    if isinstance(table_names, str):
        logger.info(f"传入的表名是字符串: {table_names}")
        if '[' in table_names and ']' in table_names:
            logger.info(f"表名是数组字符串，尝试解析为列表")    
            table_names = json.loads(table_names)
        else:
            table_names = [table_names]

    ddl_results = []

    for table_name in table_names:
        # 根据表名构造文件名
        filename = f"{table_name}_ddl.sql"
        # 使用资源管理器加载DDL文件
        ddl_content = load_resource('tables_ddl', filename)

        if ddl_content is None:
            logger.warning(f"DDL文件未找到: {filename}, 将尝试从数据库中获取DDL")
            # 尝试从数据库中获取DDL
            sql = f"show create table {table_name}"
            result = execute_business_query(sql)
            if result.success:
                ddl_content = result.data
            else:
                ddl_content = f"Error, table not found: {table_name}"

        ddl_results.append(ddl_content)

    return "\n---\n".join(ddl_results)

tool_manager.register_as_function_tool(fetch_ddl_for_table)