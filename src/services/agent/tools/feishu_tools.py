"""
为agent机器人提供飞书集成工具，用于将SQL查询结果上传到飞书云文档，并根据内容大小动态调整展示的行数。
"""
import os
from datetime import datetime
from dataclasses import dataclass
from typing import List

from src.utils.logger import logger
from src.models.user_info_class import UserInfo
from src.models.query_result import SQLQueryResult
from src.services.feishu.import_csv_to_feishu_bitable import create_bitable_and_upload_csv
from src.services.feishu.drive_service import DriveService

# 配置
min_rows = int(os.getenv("MIN_ROWS_TO_IMPORT", 20))  # 默认最小导入行数为20行
min_rows_to_cutoff = int(os.getenv("MIN_ROWS_TO_CUTOFF", 400))  # 默认最小截断行数为400行
max_content_size = int(os.getenv("MAX_CONTENT_TO_CUTOFF", 16000))  # 默认最大内容大小为16000字节
max_upload_content_size_mb = int(os.getenv("MAX_UPLOAD_CONTENT_SIZE_MB", 20))  # 默认最大上传内容大小为20MB

# 常量
SAMPLE_SIZE_FOR_CALCULATION = 100  # 用于计算平均行大小的样本数量
DISPLAY_BUFFER_BYTES = 100  # 显示计算的缓冲区大小
UPLOAD_BUFFER_BYTES = 1024  # 上传计算的缓冲区大小
MIN_DISPLAY_ROWS = 10  # 最小显示行数
MIN_UPLOAD_ROWS = 1  # 最小上传行数


@dataclass
class CSVProcessingResult:
    """CSV处理结果"""
    header_row: str
    data_rows: List[str]
    total_rows: int
    avg_row_size_bytes: float
    header_size_bytes: int


@dataclass
class RowCalculationResult:
    """行数计算结果"""
    display_rows: int
    upload_rows: int
    is_display_truncated: bool
    is_upload_truncated: bool


class CSVProcessor:
    """CSV处理器 - 负责CSV格式化和转义"""

    @staticmethod
    def escape_csv_field(field) -> str:
        """转义CSV字段"""
        if field is None:
            return ""
        field_str = str(field)
        # 如果字段包含逗号、引号或换行符，则需要用引号包围并处理内部引号
        if ',' in field_str or '"' in field_str or '\n' in field_str:
            # 将字段中的引号替换为两个引号（CSV标准转义方式）
            field_str = field_str.replace('"', '""')
            # 用引号包围整个字段
            return f'"{field_str}"'
        return field_str

    @classmethod
    def process_data_to_csv(cls, columns: List[str], rows: List[List]) -> CSVProcessingResult:
        """将数据处理为CSV格式并计算相关指标"""
        # 处理表头和数据行
        header_row = ",".join(map(cls.escape_csv_field, columns))
        data_rows = [",".join(map(cls.escape_csv_field, row)) for row in rows]

        # 计算指标
        total_rows = len(data_rows)
        header_size_bytes = len(header_row.encode('utf-8'))

        # 计算平均行大小
        avg_row_size_bytes = 0.0
        if total_rows > 0:
            sample_size = min(SAMPLE_SIZE_FOR_CALCULATION, total_rows)
            sample_content = "\n".join(data_rows[:sample_size])
            avg_row_size_bytes = len(sample_content.encode('utf-8')) / sample_size

        return CSVProcessingResult(
            header_row=header_row,
            data_rows=data_rows,
            total_rows=total_rows,
            avg_row_size_bytes=avg_row_size_bytes,
            header_size_bytes=header_size_bytes
        )


class RowCalculator:
    """行数计算器 - 负责计算显示和上传的行数"""

    @staticmethod
    def calculate_display_rows(csv_result: CSVProcessingResult) -> int:
        """计算用于显示的行数"""
        if csv_result.total_rows == 0:
            return 0

        # 估算全部内容的大小
        estimated_full_size = csv_result.header_size_bytes + (csv_result.avg_row_size_bytes * csv_result.total_rows)

        if estimated_full_size > max_content_size:
            # 计算在最大内容大小限制下可以保留的行数
            available_size = max_content_size - csv_result.header_size_bytes - DISPLAY_BUFFER_BYTES
            display_rows = int(available_size / csv_result.avg_row_size_bytes)

            # 确保至少显示一些行，但不超过min_rows_to_cutoff
            display_rows = max(MIN_DISPLAY_ROWS, min(display_rows, min_rows_to_cutoff))
            logger.info(f"内容大小估算: {estimated_full_size}字节，超过限制{max_content_size}字节，将截断至{display_rows}行")
            return display_rows
        else:
            # 如果内容大小在限制范围内，使用默认的min_rows_to_cutoff
            display_rows = min(min_rows_to_cutoff, csv_result.total_rows)
            logger.info(f"内容大小估算: {estimated_full_size}字节，在限制范围内，将显示{display_rows}行")
            return display_rows

    @staticmethod
    def calculate_upload_rows(csv_result: CSVProcessingResult) -> int:
        """计算用于上传的行数"""
        if csv_result.total_rows == 0:
            return 0

        # 生成完整CSV内容并检查大小
        full_csv_content = "\n".join([csv_result.header_row] + csv_result.data_rows)
        csv_content_size_mb = len(full_csv_content.encode('utf-8')) / (1024 * 1024)

        if csv_content_size_mb <= max_upload_content_size_mb:
            return csv_result.total_rows

        logger.info(f"CSV内容大小: {csv_content_size_mb:.2f}MB，超过上传限制{max_upload_content_size_mb}MB，需要截断数据")

        # 计算在大小限制下可以上传的最大行数
        max_upload_size_bytes = max_upload_content_size_mb * 1024 * 1024
        available_size_for_rows = max_upload_size_bytes - csv_result.header_size_bytes - UPLOAD_BUFFER_BYTES
        max_uploadable_rows = int(available_size_for_rows / csv_result.avg_row_size_bytes)

        # 确保至少上传一些行，但不超过总行数
        upload_rows = max(MIN_UPLOAD_ROWS, min(max_uploadable_rows, csv_result.total_rows))
        logger.info(f"截断后将上传{upload_rows}行数据（共{csv_result.total_rows}行）")
        return upload_rows

    @classmethod
    def calculate_rows(cls, csv_result: CSVProcessingResult) -> RowCalculationResult:
        """计算显示和上传的行数"""
        display_rows = cls.calculate_display_rows(csv_result)
        upload_rows = cls.calculate_upload_rows(csv_result)

        return RowCalculationResult(
            display_rows=display_rows,
            upload_rows=upload_rows,
            is_display_truncated=display_rows < csv_result.total_rows,
            is_upload_truncated=upload_rows < csv_result.total_rows
        )


class MessageGenerator:
    """消息生成器 - 负责生成用户消息"""

    @staticmethod
    def generate_success_message(
        total_rows: int,
        display_rows: int,
        upload_rows: int,
        is_upload_truncated: bool,
        document_name: str,
        document_url: str
    ) -> str:
        """生成成功上传的消息"""
        if is_upload_truncated:
            feishu_link_info = f"由于文件大小限制（{max_upload_content_size_mb}MB），已将前{upload_rows}条记录（共{total_rows}条）上传至飞书文档：[{document_name}]({document_url})"
        else:
            feishu_link_info = f"完整的{total_rows}条记录详情已经上传至飞书文档：[{document_name}]({document_url})"

        return f"查询结果行数:{total_rows}，现展示了前{display_rows}行。请务必以这种格式告知用户，否则用户无法获取完整数据：\n{feishu_link_info}"

    @staticmethod
    def generate_auth_error_message(total_rows: int) -> str:
        """生成认证错误消息"""
        return f"查询结果行数:{total_rows}，但无法上传到飞书文档。\n\n**原因**: 您的飞书登录状态已过期（token失效）。\n**解决方案**: 请重新登录飞书系统以重新授权，然后重试查询。\n\n💡 **提示**: 这通常发生在长时间未使用系统或飞书token过期时。"

    @staticmethod
    def generate_upload_error_message(total_rows: int, error_msg: str) -> str:
        """生成上传错误消息"""
        return f"查询结果行数:{total_rows}，但上传过程中出错: {error_msg}"

    @staticmethod
    def generate_no_upload_message(total_rows: int) -> str:
        """生成不需要上传的消息"""
        return f"查询结果行数:{total_rows}，已经展示完毕。"


class FeishuPermissionValidator:
    """飞书权限验证器"""

    @staticmethod
    def validate_access_token(user_info: UserInfo) -> bool:
        """验证用户访问令牌是否有效"""
        return user_info.access_token and user_info.access_token.strip() != ""

    @staticmethod
    def is_auth_error(error_msg: str) -> bool:
        """判断是否为认证相关错误"""
        auth_error_keywords = [
            "飞书访问令牌无效或已过期",
            "Missing access token",
            "refresh token not found"
        ]
        return any(keyword in error_msg for keyword in auth_error_keywords)


class FeishuFolderService:
    """飞书文件夹服务"""

    @staticmethod
    def get_user_folder_token(open_id: str) -> str:
        """获取用户的ChatBI文件夹token"""
        try:
            folder_token = DriveService.ensure_user_chatbi_folder(open_id)
            if folder_token:
                logger.info(f"将在用户的ChatBI文件夹中创建文档: folder_token={folder_token}")
                return folder_token
            else:
                logger.warning(f"无法获取用户ChatBI文件夹，将在根目录创建文档")
                return None
        except Exception as e:
            logger.error(f"获取用户ChatBI文件夹时出错: {e}，将在根目录创建文档")
            return None


async def upload_sql_result_to_feishu_if_needed(
        sql_result: SQLQueryResult,
        sql_description: str,
        user_info: UserInfo,
        upload_to_feishu: bool = False
) -> SQLQueryResult:
    """
    根据条件将SQL查询结果上传到飞书云文档，并调整展示的行数。

    采用领域驱动设计，将复杂逻辑拆分为多个专门的服务类：
    - CSVProcessor: 处理CSV格式化和转义
    - RowCalculator: 计算显示和上传的行数
    - MessageGenerator: 生成用户消息
    - FeishuPermissionValidator: 验证权限
    - FeishuFolderService: 处理文件夹相关操作
    """
    # 早期返回：检查数据是否为空
    if not sql_result.data:
        sql_result.message = "查询结果为空。"
        sql_result.success = True
        return sql_result

    # 早期返回：检查是否需要上传
    if not upload_to_feishu and len(sql_result.data) < min_rows:
        sql_result.message = MessageGenerator.generate_no_upload_message(len(sql_result.data))
        sql_result.success = True
        return sql_result

    # 处理CSV数据
    csv_result = CSVProcessor.process_data_to_csv(sql_result.columns, sql_result.data)

    # 计算行数
    row_calc = RowCalculator.calculate_rows(csv_result)

    # 生成文档名称
    date_of_now = datetime.now().strftime("%Y-%m-%d")
    document_name = f"{date_of_now}_{sql_description[:100]}"  # 限制名称长度为100个字符

    try:
        # 验证权限
        if not FeishuPermissionValidator.validate_access_token(user_info):
            logger.error(f"用户access_token为空，无法创建飞书文档: user={user_info.user_name}, open_id={user_info.open_id}")
            sql_result.message = MessageGenerator.generate_auth_error_message(csv_result.total_rows)
            sql_result.error = "用户飞书登录状态过期，需要重新登录"
            return sql_result

        # 获取文件夹token
        folder_token = FeishuFolderService.get_user_folder_token(user_info.open_id)

        # 生成用于上传的CSV内容
        upload_csv_content = "\n".join([csv_result.header_row] + csv_result.data_rows[:row_calc.upload_rows])

        # 执行上传
        logger.info(f"即将为用户:{user_info.user_name}创建云文档:{document_name}, access_token:{user_info.access_token[:20]}..., open_id:{user_info.open_id}")
        result = await create_bitable_and_upload_csv(
            csv_content=upload_csv_content,
            access_token=user_info.access_token,
            name=document_name,
            folder_token=folder_token,
        )

        # 截断数据以便展示
        sql_result.data = sql_result.data[:row_calc.display_rows]

        # 处理上传结果
        if result and result.status == "success":
            sql_result.message = MessageGenerator.generate_success_message(
                total_rows=csv_result.total_rows,
                display_rows=row_calc.display_rows,
                upload_rows=row_calc.upload_rows,
                is_upload_truncated=row_calc.is_upload_truncated,
                document_name=document_name,
                document_url=result.url
            )
            sql_result.success = True
            sql_result.feishu_url = result.url
            sql_result.feishu_app_token = getattr(result, 'app_token', None)
            sql_result.feishu_table_id = getattr(result, 'table_id', None)
        else:
            sql_result.message = "查询结果上传失败"
            sql_result.error = "查询结果上传失败"

    except Exception as e:
        error_msg = str(e)
        logger.error(f"上传查询结果到飞书时出错: {error_msg}")

        # 处理不同类型的错误
        if FeishuPermissionValidator.is_auth_error(error_msg):
            sql_result.message = MessageGenerator.generate_auth_error_message(csv_result.total_rows)
            sql_result.error = "飞书登录状态过期，需要重新登录"
        else:
            sql_result.message = MessageGenerator.generate_upload_error_message(csv_result.total_rows, error_msg)
            sql_result.error = f"上传过程错误: {error_msg}"

    return sql_result
