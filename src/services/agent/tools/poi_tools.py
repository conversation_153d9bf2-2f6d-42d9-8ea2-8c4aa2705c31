"""
POI（兴趣点）相关工具函数

提供位置信息获取和POI搜索功能，支持：
1. 解析用户位置信息
2. 根据关键词搜索POI（使用高德地图API）
"""

import json
import aiohttp
from typing import Optional, Dict, Any, List

from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.logger import logger


async def get_user_location(wrapper: RunContextWrapper[UserInfo]) -> str:
    """
    获取用户的当前位置信息

    Args:
        wrapper: 调用的上下文

    Returns:
        str: 用户位置信息的描述
    """
    logger.info("获取用户位置信息")

    # 从用户信息中获取位置信息
    user_info = wrapper.context

    # 检查用户信息中是否有位置信息
    if hasattr(user_info, 'location') and user_info.location:
        location = user_info.location
        latitude = location.get('latitude')
        longitude = location.get('longitude')
        accuracy = location.get('accuracy')
        timestamp = location.get('timestamp')

        if latitude and longitude:
            logger.info(f"用户位置: 纬度={latitude}, 经度={longitude}, 精度={accuracy}米")

            # 格式化时间戳
            import datetime
            if timestamp:
                location_time = datetime.datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
            else:
                location_time = "未知"

            return f"""用户位置信息：
- 纬度：{latitude}
- 经度：{longitude}
- 定位精度：{accuracy}米
- 获取时间：{location_time}
- 位置坐标：{latitude},{longitude}

您可以使用这个位置信息来搜索附近的POI（兴趣点）。"""
        else:
            return "用户位置信息不完整，缺少经纬度坐标。"
    else:
        return """用户尚未提供位置信息。

可能的原因：
1. 用户拒绝了浏览器的位置权限请求
2. 用户的设备不支持地理定位
3. 网络问题导致位置获取失败

建议：
- 请用户刷新页面并允许位置访问
- 或者让用户提供具体的地址信息，如"杭州市西湖区"等"""


async def search_poi_by_keyword(wrapper: RunContextWrapper[UserInfo],
                                keywords: str) -> str:
    """
    根据关键词搜索POI（兴趣点）

    Args:
        wrapper: 调用的上下文
        keywords: 搜索关键词，如"新杭商务中心"、"星巴克"等

    Returns:
        str: 搜索结果的格式化文本
    """
    logger.info(f"搜索POI: 关键词={keywords}")

    if not keywords or not keywords.strip():
        return "请提供搜索关键词"

    # 从用户信息中获取位置信息
    user_info = wrapper.context
    location = None

    # 检查用户信息中是否有位置信息
    if hasattr(user_info, 'location') and user_info.location:
        location = f"{user_info.location.get('latitude', '')},{user_info.location.get('longitude', '')}"
        logger.info(f"从用户信息中获取的位置: {location}")
    else:
        logger.info(f"用户信息中没有位置信息，user_info: {user_info}")

    try:
        # 高德地图API配置
        api_key = "d50b07176cd546d73828ec9f2f5c3f57"
        base_url = "https://restapi.amap.com/v5/place/text"

        # 构建请求参数
        params = {
            "keywords": keywords.strip(),
            "key": api_key,
            "output": "json"
        }

        # 如果提供了位置信息，添加到参数中
        if location and "," in location:
            params["location"] = location
            params["radius"] = "5000"  # 5公里范围内搜索

        logger.info(f"调用高德API，参数: {params}")

        # 发起API请求
        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, params=params) as response:
                if response.status != 200:
                    return f"API请求失败，状态码: {response.status}"

                data = await response.json()
                logger.info(f"高德API响应: {json.dumps(data, ensure_ascii=False, indent=2)}")

                # 检查API响应状态
                if data.get("status") != "1":
                    error_info = data.get("info", "未知错误")
                    return f"搜索失败: {error_info}"

                # 解析POI数据
                pois = data.get("pois", [])
                if not pois:
                    return f"未找到与'{keywords}'相关的地点"

                # 格式化搜索结果
                result_lines = [f"搜索关键词：{keywords}"]
                if location:
                    result_lines.append(f"搜索范围：以 {location} 为中心5公里内")
                result_lines.append(f"找到 {len(pois)} 个相关地点：\n")

                for i, poi in enumerate(pois[:10], 1):  # 最多显示10个结果
                    name = poi.get("name", "未知名称")
                    address = poi.get("address", "")
                    location_coords = poi.get("location", "")
                    poi_type = poi.get("type", "")
                    cityname = poi.get("cityname", "")
                    adname = poi.get("adname", "")

                    # 构建完整地址
                    full_address = ""
                    if cityname:
                        full_address += cityname
                    if adname and adname != cityname:
                        full_address += adname
                    if address:
                        full_address += address

                    result_lines.append(f"{i}. **{name}**")
                    if full_address:
                        result_lines.append(f"   地址：{full_address}")
                    if location_coords:
                        result_lines.append(f"   坐标：{location_coords}")
                    if poi_type:
                        result_lines.append(f"   类型：{poi_type}")
                    result_lines.append("")  # 空行分隔

                return "\n".join(result_lines)

    except Exception as e:
        logger.error(f"搜索POI时出错: {e}")
        return f"搜索POI时出错: {str(e)}"


async def search_nearby_customers(wrapper: RunContextWrapper[UserInfo],
                                  longitude: float,
                                  latitude: float,
                                  distance: int = 500,
                                  query_type: Optional[int] = None,
                                  page_size: int = 5,
                                  page_index: int = 1) -> str:
    """
    根据POI位置查询附近的客户.
    该接口为分页查询. 若有需要,可多次调用或调高page_size以获取更多或完整客户列表

    Args:
        wrapper: 调用的上下文
        longitude: 经度
        latitude: 纬度
        distance: 搜索半径（米），默认500米,
        query_type: 可选, 搜索类型： 0、用户私海里的客户 1、别人私海里的客户 2、公海客户 3、未注册客户, 不传时则查询所有客户
        page_size: 每页显示的客户数量
        page_index: 查询的页码

    Returns:
        str: 附近客户信息的描述
    """
    logger.info(f"搜索附近客户: 位置=({latitude},{longitude}), 半径={distance}米")

    # 从用户信息中获取API token
    user_info = wrapper.context

    if not hasattr(user_info, 'summerfarm_api_token') or not user_info.summerfarm_api_token:
        logger.error("用户信息中缺少summerfarm_api_token")
        return "无法查询附近客户：缺少必要的API访问权限。请联系管理员配置用户权限。"

    api_token = user_info.summerfarm_api_token

    # 构建API请求URL
    base_url = f"https://admin.summerfarm.net/crm-service/crm/nearby/{page_index}/{page_size}"

    # 构建查询参数
    params = {
        'poi.lon': longitude,
        'poi.lat': latitude,
        'distance': distance
    }
    if query_type is not None:
        params['queryType'] = query_type

    # 构建请求头
    headers = {
        'token': api_token,
        'User-Agent': 'ChatBI/1.0.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    try:
        logger.info(f"调用客户查询API，参数: {params}")

        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"客户查询API响应成功: {response.status}")

                    # 解析响应数据
                    if data.get('success', False) or data.get('code') == 'SUCCESS':
                        logger.info(f"客户查询API成功返回数据")

                        # 简化数据，只保留关键信息以节约token
                        simplified_data = {
                            "code": data.get('code'),
                            "success": data.get('success'),
                            "data": {
                                "total": data.get('data', {}).get('total', 0),
                                "pageNum": data.get('data', {}).get('pageNum', 1),
                                "pageSize": data.get('data', {}).get('pageSize', 10),
                                "lastPage": data.get('data', {}).get('lastPage', 1),
                                "list": []
                            }
                        }

                        # 只保留客户的核心信息：mId, mname, distance
                        customer_list = data.get('data', {}).get('list', [])
                        for customer in customer_list:
                            simplified_customer = {
                                "mId": customer.get('mId'),
                                "mname": customer.get('mname'),
                                "distance": customer.get('distance', 0)
                            }
                            simplified_data["data"]["list"].append(simplified_customer)

                        import json
                        return f"""查询成功！在位置 ({latitude}, {longitude}) 半径 {distance} 米范围内的客户数据：

```json
{json.dumps(simplified_data, ensure_ascii=False, indent=2)}
```

请根据以上数据为用户提供清晰的客户信息总结。"""

                    else:
                        error_msg = data.get('message', '未知错误')
                        logger.error(f"客户查询API返回错误: {error_msg}")
                        return f"查询附近客户失败：{error_msg}"

                else:
                    error_text = await response.text()
                    logger.error(f"客户查询API请求失败: {response.status}, {error_text}")
                    return f"查询附近客户失败：服务器返回错误 {response.status}"

    except Exception as e:
        logger.error(f"查询附近客户时发生异常: {str(e)}")
        return f"查询附近客户时发生错误：{str(e)}"


# 注册工具函数
tool_manager.register_as_function_tool(get_user_location)
tool_manager.register_as_function_tool(search_poi_by_keyword)
tool_manager.register_as_function_tool(search_nearby_customers)
