"""
用户Session领域服务

负责用户session的业务逻辑处理
"""

from typing import Optional, Tuple, List
from datetime import datetime, timedelta

from src.models.user_session import UserSession, SessionToken
from src.repositories.chatbi.user_session import UserSessionRepository
from src.utils.logger import logger


class UserSessionService:
    """用户Session领域服务"""
    
    def __init__(self):
        self.repository = UserSessionRepository()
    
    def create_user_session(self, open_id: str, refresh_token: str, 
                           access_token: Optional[str] = None,
                           access_token_expires_at: Optional[datetime] = None) -> Optional[str]:
        """
        创建新的用户session（实现单点登录）
        
        Args:
            open_id: 飞书用户open_id
            refresh_token: 飞书refresh token
            access_token: 当前access token
            access_token_expires_at: access token过期时间
            
        Returns:
            Optional[str]: 创建成功返回session_id，失败返回None
        """
        try:
            # 先停用该用户的所有现有session（实现单点登录）
            self.repository.deactivate_all_by_open_id(open_id)
            
            # 创建新session
            session = UserSession.create_new_session(
                open_id=open_id,
                refresh_token=refresh_token,
                access_token=access_token,
                access_token_expires_at=access_token_expires_at
            )
            
            # 保存到数据库
            if self.repository.save(session):
                logger.info(f"用户session创建成功: open_id={open_id}, session_id={session.session_id}")
                return session.session_id
            else:
                logger.error(f"用户session保存失败: open_id={open_id}")
                return None
                
        except Exception as e:
            logger.error(f"创建用户session失败: {e}", exc_info=True)
            return None
    
    def get_session_by_id(self, session_id: str) -> Optional[UserSession]:
        """
        根据session_id获取session
        
        Args:
            session_id: session ID
            
        Returns:
            Optional[UserSession]: session实例，不存在或已失效返回None
        """
        try:
            session = self.repository.find_by_session_id(session_id)
            if session and not session.is_expired():
                # 更新最后活跃时间
                session.mark_active()
                self.repository.update(session)
                return session
            return None
        except Exception as e:
            logger.error(f"获取用户session失败: {e}", exc_info=True)
            return None
    
    def update_session_tokens(self, session_id: str, access_token: str, 
                             refresh_token: str, expires_at: datetime) -> bool:
        """
        更新session中的token信息
        
        Args:
            session_id: session ID
            access_token: 新的access token
            refresh_token: 新的refresh token
            expires_at: access token过期时间
            
        Returns:
            bool: 是否更新成功
        """
        try:
            session = self.repository.find_by_session_id(session_id)
            if not session:
                logger.warning(f"未找到session: session_id={session_id}")
                return False
            
            # 更新token信息
            session.access_token = access_token
            session.refresh_token = refresh_token
            session.update_access_token(access_token, expires_at)
            
            return self.repository.update(session)
        except Exception as e:
            logger.error(f"更新session tokens失败: {e}", exc_info=True)
            return False
    
    def recover_refresh_token(self, session_id: str) -> Optional[str]:
        """
        从session中恢复refresh token
        
        Args:
            session_id: session ID
            
        Returns:
            Optional[str]: refresh token，失败返回None
        """
        try:
            session = self.repository.find_by_session_id(session_id)
            if session and not session.is_expired():
                logger.info(f"成功从session恢复refresh token: session_id={session_id}")
                # 更新最后活跃时间
                session.mark_active()
                self.repository.update(session)
                return session.refresh_token
            else:
                logger.warning(f"session不存在或已过期: session_id={session_id}")
                return None
        except Exception as e:
            logger.error(f"恢复refresh token失败: {e}", exc_info=True)
            return None
    
    def logout_session(self, session_id: str) -> bool:
        """
        登出指定session
        
        Args:
            session_id: session ID
            
        Returns:
            bool: 是否操作成功
        """
        try:
            return self.repository.deactivate_by_session_id(session_id)
        except Exception as e:
            logger.error(f"登出session失败: {e}", exc_info=True)
            return False
    
    def invalidate_session(self, session_id: str) -> bool:
        """
        使指定session失效（当refresh token失效时使用）
        
        Args:
            session_id: session ID
            
        Returns:
            bool: 是否操作成功
        """
        try:
            logger.info(f"使session失效: session_id={session_id}")
            return self.repository.deactivate_by_session_id(session_id)
        except Exception as e:
            logger.error(f"使session失效失败: {e}", exc_info=True)
            return False
    
    def logout_all_sessions(self, open_id: str) -> bool:
        """
        登出用户的所有session
        
        Args:
            open_id: 用户open_id
            
        Returns:
            bool: 是否操作成功
        """
        try:
            return self.repository.deactivate_all_by_open_id(open_id)
        except Exception as e:
            logger.error(f"登出用户所有session失败: {e}", exc_info=True)
            return False
    
    def cleanup_expired_sessions(self, max_inactive_days: int = 30) -> int:
        """
        清理过期的session
        
        Args:
            max_inactive_days: 最大非活跃天数
            
        Returns:
            int: 清理的session数量
        """
        try:
            return self.repository.cleanup_expired_sessions(max_inactive_days)
        except Exception as e:
            logger.error(f"清理过期session失败: {e}", exc_info=True)
            return 0
    
    def validate_session_token(self, session_id: str, access_token: str) -> bool:
        """
        验证session中的access token是否匹配
        
        Args:
            session_id: session ID
            access_token: 要验证的access token
            
        Returns:
            bool: 是否匹配
        """
        try:
            session = self.repository.find_by_session_id(session_id)
            if session and session.access_token == access_token:
                return True
            return False
        except Exception as e:
            logger.error(f"验证session token失败: {e}", exc_info=True)
            return False
    
    def get_user_active_sessions_count(self, open_id: str) -> int:
        """
        获取用户活跃session数量
        
        Args:
            open_id: 用户open_id
            
        Returns:
            int: 活跃session数量
        """
        try:
            sessions = self.repository.find_active_sessions_by_open_id(open_id)
            return len(sessions)
        except Exception as e:
            logger.error(f"获取用户活跃session数量失败: {e}", exc_info=True)
            return 0
    
    def is_session_near_expiry(self, session_id: str, minutes_threshold: int = 10) -> bool:
        """
        检查session中的access token是否即将过期

        Args:
            session_id: session ID
            minutes_threshold: 过期阈值（分钟）

        Returns:
            bool: 是否即将过期
        """
        try:
            session = self.repository.find_by_session_id(session_id)
            if session and session.access_token_expires_at:
                time_diff = (session.access_token_expires_at - datetime.now()).total_seconds() / 60
                return time_diff <= minutes_threshold
            return True  # 如果没有过期时间信息，认为需要刷新
        except Exception as e:
            logger.error(f"检查session过期状态失败: {e}", exc_info=True)
            return True

    def get_sessions_near_expiry(self, minutes_threshold: int = 10) -> List[UserSession]:
        """
        获取即将过期的sessions

        Args:
            minutes_threshold: 过期阈值（分钟）

        Returns:
            List[UserSession]: 即将过期的session列表
        """
        try:
            return self.repository.find_sessions_near_expiry(minutes_threshold)
        except Exception as e:
            logger.error(f"获取即将过期的sessions失败: {e}", exc_info=True)
            return []


# 全局服务实例
user_session_service = UserSessionService()
