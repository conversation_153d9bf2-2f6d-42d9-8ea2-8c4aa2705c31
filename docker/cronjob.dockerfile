FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    cron \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt /app/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目代码
COPY . /app/

# 创建日志目录
RUN mkdir -p /var/log/chatbi

# 复制crontab配置文件
COPY docker/offline_recommendation.cron /etc/cron.d/offline_recommendation

# 设置文件权限
RUN chmod 0644 /etc/cron.d/offline_recommendation
RUN chmod +x /app/scripts/run_offline_recommendation.py

# 启动cron并设置日志输出
CMD cron -f && tail -f /var/log/chatbi/offline_recommendation.log