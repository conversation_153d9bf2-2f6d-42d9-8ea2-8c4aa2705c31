#!/usr/bin/env python3
"""
离线推荐任务执行脚本。
用于定时执行用户推荐问题的离线生成任务。
"""

import asyncio
import logging
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.services.recommendation.offline_recommendation_service import OfflineRecommendationService
from src.database.mysql_client import MySQLClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/var/log/chatbi/offline_recommendation.log')
    ]
)
logger = logging.getLogger(__name__)


async def main():
    """主执行逻辑"""
    logger.info("开始执行离线推荐任务...")
    
    try:
        # 初始化服务
        mysql_client = MySQLClient()
        service = OfflineRecommendationService(mysql_client)
        
        # 执行每日推荐任务
        await service.run_daily_recommendation_task()
        
        logger.info("离线推荐任务执行完成")
        
    except Exception as e:
        logger.error(f"离线推荐任务执行失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())